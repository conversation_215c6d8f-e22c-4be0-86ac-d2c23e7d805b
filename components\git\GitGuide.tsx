'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { GitBranch, Settings, GitCommit, Upload, Download } from 'lucide-react';

export function GitGuide() {
  return (
    <div className="p-4 space-y-4 max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <GitBranch className="w-5 h-5" />
            <span>Git工作流使用指南</span>
          </CardTitle>
          <CardDescription>
            学习如何在Markdown编辑器中使用Git版本控制功能
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 步骤1 */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center">1</Badge>
              <h3 className="text-lg font-semibold flex items-center space-x-2">
                <Settings className="w-4 h-4" />
                <span>配置Git信息</span>
              </h3>
            </div>
            <div className="ml-8 space-y-2">
              <p className="text-muted-foreground">
                首先需要设置您的Git用户信息，这些信息会在提交时使用。
              </p>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>点击左侧边栏的Git图标</li>
                <li>点击"设置Git配置"按钮</li>
                <li>填写您的用户名和邮箱</li>
                <li>可选：填写GitHub Personal Access Token（用于私有仓库）</li>
              </ul>
            </div>
          </div>

          {/* 步骤2 */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center">2</Badge>
              <h3 className="text-lg font-semibold flex items-center space-x-2">
                <GitBranch className="w-4 h-4" />
                <span>连接或创建仓库</span>
              </h3>
            </div>
            <div className="ml-8 space-y-2">
              <p className="text-muted-foreground">
                连接到现有的GitHub仓库或创建一个新的本地仓库。
              </p>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>点击"连接仓库"按钮</li>
                <li>选择"克隆仓库"或"新建仓库"</li>
                <li>如果克隆：输入GitHub仓库信息</li>
                <li>如果新建：系统会自动创建本地仓库</li>
              </ul>
            </div>
          </div>

          {/* 步骤3 */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center">3</Badge>
              <h3 className="text-lg font-semibold flex items-center space-x-2">
                <GitCommit className="w-4 h-4" />
                <span>编辑和提交</span>
              </h3>
            </div>
            <div className="ml-8 space-y-2">
              <p className="text-muted-foreground">
                在编辑器中修改Markdown文档，然后提交您的更改。
              </p>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>在编辑器中编写或修改Markdown内容</li>
                <li>在Git面板中查看文件变化</li>
                <li>输入提交信息描述您的更改</li>
                <li>点击"提交"按钮保存更改</li>
              </ul>
            </div>
          </div>

          {/* 步骤4 */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center">4</Badge>
              <h3 className="text-lg font-semibold flex items-center space-x-2">
                <Upload className="w-4 h-4" />
                <span>推送和同步</span>
              </h3>
            </div>
            <div className="ml-8 space-y-2">
              <p className="text-muted-foreground">
                将本地更改推送到远程仓库，或从远程仓库拉取最新更改。
              </p>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>点击"推送"按钮将更改上传到GitHub</li>
                <li>点击"拉取"按钮获取远程更新</li>
                <li>查看提交历史了解项目进展</li>
                <li>使用分支信息跟踪当前工作分支</li>
              </ul>
            </div>
          </div>

          {/* 功能特性 */}
          <div className="space-y-3 pt-4 border-t">
            <h3 className="text-lg font-semibold">功能特性</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">版本控制</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li>完整的Git工作流支持</li>
                  <li>提交历史查看</li>
                  <li>文件状态跟踪</li>
                  <li>分支信息显示</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">数据管理</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li>本地数据持久化</li>
                  <li>配置信息保存</li>
                  <li>数据导入导出</li>
                  <li>自动备份机制</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 注意事项 */}
          <div className="space-y-3 pt-4 border-t">
            <h3 className="text-lg font-semibold">注意事项</h3>
            <div className="bg-muted p-4 rounded-lg">
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>当前版本使用本地模拟的Git功能，推送和拉取操作会显示成功消息</li>
                <li>所有数据保存在浏览器的本地存储中</li>
                <li>建议定期导出数据进行备份</li>
                <li>GitHub Token仅用于未来的真实API集成</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
