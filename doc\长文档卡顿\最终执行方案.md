# Markdown编辑器性能优化 - 最终执行方案

> **硬指标目标**：滚动 ≥ 55 FPS；输入→预览 P95 < 150ms；单次长任务 < 50ms；预览区 DOM 子节点 ≤ 2500；L档长文连续滚动 2分钟内存不持续上升 ≥ 10%

## 📋 现状确认

### 已确认的性能问题 ✅

通过代码审查，以下问题确实存在：

```typescript
// ❌ 问题1：不稳定段落ID (NativeDOMRenderer.tsx:95)
segments.push({
  id: `segment-${segmentIndex}`,  // 基于索引，插入内容会导致所有后续ID变化
  content: segmentContent,
  hash: hashString(segmentContent)
});

// ❌ 问题2：全量DOM替换 (NativeDOMRenderer.tsx:198-199)
container.innerHTML = '';           // 清空容器，丢失所有DOM状态
container.appendChild(fragment);    // 整体重建，触发大量布局计算

// ❌ 问题3：主线程同步解析 (NativeDOMRenderer.tsx:48)
let result = processor.processSync(content);  // 阻塞主线程

// ❌ 问题4：无防抖机制 (CodeMirrorEditor.tsx:82)
onChange(newValue);  // 每次击键都触发完整渲染链路

// ❌ 问题5：强制滚动位置 (NativeDOMRenderer.tsx:202)
container.scrollTop = currentScrollTop;  // 内容高度变化时容易跳动
```

## 🎯 三阶段实施方案

### Milestone 1（T+2天）- 立即见效

#### 目标指标
- 输入延迟降低 50%（通过防抖）
- DOM重建减少 80%（通过稳定ID）
- 滚动抖动基本消除

#### 核心任务

**1. 稳定段落ID实现**

```typescript
// 新增：lib/boundary-index.ts
export class BoundaryIndex {
  private lineStart: Uint32Array;
  private content: string;

  constructor(content: string) {
    this.content = content;
    this.lineStart = this.buildLineStartArray(content);
  }

  private buildLineStartArray(content: string): Uint32Array {
    const lines = content.split('\n');
    const lineStart = new Uint32Array(lines.length + 1);
    let offset = 0;

    for (let i = 0; i < lines.length; i++) {
      lineStart[i] = offset;
      offset += lines[i].length + 1; // +1 for '\n'
    }
    lineStart[lines.length] = offset - 1;
    return lineStart;
  }

  toOffset(pos: { line: number; col: number }): number {
    return this.lineStart[pos.line] + pos.col;
  }

  toPos(offset: number): { line: number; col: number } {
    // 二分查找
    let left = 0, right = this.lineStart.length - 1;
    while (left < right) {
      const mid = Math.floor((left + right + 1) / 2);
      if (this.lineStart[mid] <= offset) {
        left = mid;
      } else {
        right = mid - 1;
      }
    }
    return {
      line: left,
      col: offset - this.lineStart[left]
    };
  }

  applyChanges(changes: any): void {
    // TODO: M2阶段实现增量更新
  }
}

// 新增：lib/hash-utils.ts
// 使用MurmurHash3 x86_32快速哈希
export function fastHash32(str: string): string {
  let h1 = 0x811c9dc5;
  for (let i = 0; i < str.length; i++) {
    h1 ^= str.charCodeAt(i);
    h1 += (h1 << 1) + (h1 << 4) + (h1 << 7) + (h1 << 8) + (h1 << 24);
  }
  return (h1 >>> 0).toString(36);
}

// 生成稳定段落ID
export function generateStableSegmentId(content: string, startOffset: number): string {
  const hash = fastHash32(content);
  return `${hash}:${startOffset}`;
}
```

**2. 防抖机制实现**

```typescript
// 修改：components/editor/CodeMirrorEditor.tsx
import { debounce } from 'lodash-es';

// 在组件内添加防抖逻辑
const debouncedOnChange = useMemo(() => {
  return debounce((newValue: string) => {
    requestAnimationFrame(() => {
      onChange(newValue);
    });
  }, 120, {
    leading: false,
    trailing: true
  });
}, [onChange]);

// 修改updateListener
EditorView.updateListener.of((update) => {
  if (update.docChanged) {
    const newValue = update.state.doc.toString();
    debouncedOnChange(newValue);
  }
  // 滚动事件不防抖，保持实时性
  if (update.viewportChanged && onScrollPositionChange) {
    const scrollTop = update.view.scrollDOM.scrollTop;
    onScrollPositionChange(scrollTop);
}
})
```

**3. keyed-diff DOM更新**

```typescript
// 修改：components/editor/NativeDOMRenderer.tsx
// 完全重写updateDOM函数

const updateDOMKeyed = useCallback((
  container: HTMLElement,
  oldSegments: ContentSegment[],
  newSegments: ContentSegment[]
) => {
  // 构建映射
  const oldMap = new Map(oldSegments.map(seg => [seg.id, seg]));
  const newMap = new Map(newSegments.map(seg => [seg.id, seg]));
  
  // 获取现有DOM元素映射
  const existingElements = Array.from(container.children) as HTMLElement[];
  const elementMap = new Map<string, HTMLElement>();
  
  existingElements.forEach(el => {
    const segmentId = el.getAttribute('data-segment-id');
    if (segmentId) {
      elementMap.set(segmentId, el);
    }
  });

  // 计算操作：add/remove/update/move
  const operations: Array<{
    type: 'add' | 'remove' | 'update' | 'move';
    segmentId: string;
    element?: HTMLElement;
    newIndex?: number;
    content?: string;
  }> = [];

  // 1. 标记删除
  oldSegments.forEach(oldSeg => {
    if (!newMap.has(oldSeg.id)) {
      operations.push({
        type: 'remove',
        segmentId: oldSeg.id,
        element: elementMap.get(oldSeg.id)
      });
    }
  });

  // 2. 标记新增/更新/移动
  newSegments.forEach((newSeg, index) => {
    const oldSeg = oldMap.get(newSeg.id);
    const existingElement = elementMap.get(newSeg.id);

    if (!oldSeg) {
      // 新增
      operations.push({
        type: 'add',
        segmentId: newSeg.id,
        newIndex: index,
        content: newSeg.content
      });
    } else if (oldSeg.hash !== newSeg.hash) {
      // 更新
      operations.push({
        type: 'update',
        segmentId: newSeg.id,
        element: existingElement,
        content: newSeg.content
      });
    } else if (existingElement) {
      // 检查是否需要移动
      const currentIndex = Array.from(container.children).indexOf(existingElement);
      if (currentIndex !== index) {
        operations.push({
          type: 'move',
          segmentId: newSeg.id,
          element: existingElement,
          newIndex: index
        });
      }
    }
  });

  // 3. 批量执行操作
  // 先执行删除
  operations.filter(op => op.type === 'remove').forEach(op => {
    if (op.element && op.element.parentNode) {
      op.element.parentNode.removeChild(op.element);
    }
  });

  // 构建新的有序子节点列表
  const newChildren: HTMLElement[] = [];
  newSegments.forEach((segment, index) => {
    const existingElement = elementMap.get(segment.id);
    const op = operations.find(o => o.segmentId === segment.id);

    if (op?.type === 'add') {
      // 创建新元素
      const div = document.createElement('div');
      div.className = 'markdown-segment';
      div.setAttribute('data-segment-id', segment.id);
      div.innerHTML = renderSegment(segment.content);
      newChildren.push(div);
    } else if (op?.type === 'update' && existingElement) {
      // 更新内容
      existingElement.innerHTML = renderSegment(segment.content);
      newChildren.push(existingElement);
    } else if (existingElement) {
      // 复用元素
      newChildren.push(existingElement);
    }
  });

  // 一次性重新排列所有子节点（避免多次布局）
  const fragment = document.createDocumentFragment();
  newChildren.forEach(child => fragment.appendChild(child));
  
  // ✅ 关键改进：不再使用innerHTML = ''
  while (container.firstChild) {
    container.removeChild(container.firstChild);
  }
  container.appendChild(fragment);

}, []);
```

**4. 移除强制滚动位置**

```typescript
// 修改：components/editor/NativeDOMRenderer.tsx
// 移除 container.scrollTop = currentScrollTop;
// 添加语义锚点恢复雏形

const restoreScrollPosition = useCallback(() => {
  // 暂时保持简单的位置恢复，M2阶段完善
  if (containerRef.current && typeof initialScrollPosition === 'number') {
    requestAnimationFrame(() => {
      if (containerRef.current) {
        containerRef.current.scrollTo({
          top: initialScrollPosition,
          behavior: 'auto'
        });
      }
    });
  }
}, [initialScrollPosition]);
```

**5. 性能监控埋点**

```typescript
// 新增：lib/performance-monitor.ts
export class PerformanceMonitor {
  private static longTasks: number[] = [];
  private static observer?: PerformanceObserver;

  static init() {
    // 监控长任务
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'longtask') {
            this.longTasks.push(entry.duration);
            console.warn(`Long task detected: ${entry.duration}ms`);
          }
        }
      });
      this.observer.observe({ entryTypes: ['longtask'] });
    }
  }

  static markStart(name: string) {
    performance.mark(`${name}-start`);
  }

  static markEnd(name: string) {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
  }

  static getLongTaskStats() {
    return {
      count: this.longTasks.length,
      max: Math.max(...this.longTasks, 0),
      avg: this.longTasks.reduce((a, b) => a + b, 0) / this.longTasks.length || 0
    };
  }

  static getDOMNodeCount(): number {
    return document.querySelectorAll('*').length;
  }

  static destroy() {
    this.observer?.disconnect();
}
}

// 在编辑器中使用
PerformanceMonitor.markStart('edit-to-preview');
// ... 渲染逻辑
PerformanceMonitor.markEnd('edit-to-preview');
```

**6. 安全基线**

```typescript
// 新增：lib/sanitizer.ts
import { unified } from 'unified';
import rehypeSanitize from 'rehype-sanitize';
import { defaultSchema } from 'hast-util-sanitize';

// 安全渲染配置
const secureSchema = {
  ...defaultSchema,
  attributes: {
    ...defaultSchema.attributes,
    // 为外链添加安全属性
    a: [...(defaultSchema.attributes?.a || []), 'rel', 'target'],
    // 图片懒加载
    img: [...(defaultSchema.attributes?.img || []), 'loading', 'width', 'height']
  }
};

export const secureRenderSegment = (content: string): string => {
  try {
    const processor = unified()
      .use(remarkParse)
      .use(remarkGfm)
      .use(remarkRehype, { allowDangerousHtml: false }) // 禁用危险HTML
      .use(rehypeSanitize, secureSchema) // 添加内容消毒
      .use(rehypeHighlight)
      .use(rehypeStringify);

    let result = processor.processSync(content);
    let htmlString = String(result);
    
    // 为外链添加安全属性
    htmlString = htmlString.replace(
      /<a\s+href="https?:\/\/[^"]+"/g,
      (match) => match + ' rel="noopener noreferrer" target="_blank"'
  );

    // 为图片添加懒加载
    htmlString = htmlString.replace(
      /<img/g,
      '<img loading="lazy"'
    );

    return htmlString;
  } catch (error) {
    console.error('Secure rendering error:', error);
    return '<p>渲染错误</p>';
  }
};
```

#### M1 验收标准

- [ ] S档文档输入延迟 < 100ms
- [ ] 滚动FPS ≥ 50（Chrome DevTools测量）
- [ ] 长任务 < 80ms
- [ ] DOM节点数稳定（不再增长）
- [ ] 所有现有功能正常运行

### Milestone 2（T+1周）- 结构优化

#### 目标指标
- 局部重分段：编辑延迟降低70%
- 语义锚点：滚动稳定性99%+
- 自适应防抖：各档位体验优化

#### 核心任务

**1. 局部重分段实现**

```typescript
// 完善：lib/boundary-index.ts
export class BoundaryIndex {
  // ... 之前的代码

  applyChanges(changes: ChangeDesc): void {
    let cumulativeOffset = 0;
    
    // 遍历所有变更，更新lineStart
    changes.iterChanges((fromA, toA, fromB, toB, inserted) => {
      const deletedLength = toA - fromA;
      const insertedLength = inserted.toString().length;
      const delta = insertedLength - deletedLength;
      
      // 找到受影响的行
      const startPos = this.toPos(fromA + cumulativeOffset);
      
      // 更新后续所有行的起始位置
      for (let i = startPos.line + 1; i < this.lineStart.length; i++) {
        this.lineStart[i] += delta;
      }
      
      cumulativeOffset += delta;
    });
    
    // 如果有新行插入，需要重建整个数组
    const newContent = this.content; // 需要传入新内容
    const newLineCount = newContent.split('\n').length;
    if (newLineCount !== this.lineStart.length - 1) {
      this.lineStart = this.buildLineStartArray(newContent);
    }
  }
}

// 新增：lib/incremental-parser.ts
export function calculateAffectedLineRange(
  changes: ChangeDesc, 
  oldContent: string
): { start: number; end: number } {
  let minLine = Infinity;
  let maxLine = -1;
  
  const lines = oldContent.split('\n');
  const boundaryIndex = new BoundaryIndex(oldContent);
  
  changes.iterChanges((fromA, toA) => {
    const startPos = boundaryIndex.toPos(fromA);
    const endPos = boundaryIndex.toPos(toA);
    
    minLine = Math.min(minLine, startPos.line);
    maxLine = Math.max(maxLine, endPos.line);
  });
  
  return {
    start: Math.max(0, minLine),
    end: Math.min(lines.length - 1, maxLine)
  };
}

export function findAffectedSegments(
  segments: ContentSegment[],
  lineRange: { start: number; end: number }
): { start: number; end: number } {
  // TODO: 基于段落的行范围映射
  // 外扩1段作为缓冲
  let startIndex = 0;
  let endIndex = segments.length - 1;
  
  // 简化实现，M2完善
  for (let i = 0; i < segments.length; i++) {
    // 需要维护段落的行号信息
    if (/* 段落包含受影响行 */) {
      startIndex = Math.max(0, i - 1); // 外扩1段
      break;
    }
  }
  
  for (let i = segments.length - 1; i >= 0; i--) {
    if (/* 段落包含受影响行 */) {
      endIndex = Math.min(segments.length - 1, i + 1); // 外扩1段
      break;
    }
  }
  
  return { start: startIndex, end: endIndex };
}
```

**2. 语义锚点滚动恢复**

```typescript
// 新增：lib/scroll-anchor.ts
export interface ScrollAnchor {
  type: 'heading' | 'paragraph' | 'code';
  elementId?: string;
  segmentId: string;
  offsetPercent: number; // 段内偏移百分比
}

export function createScrollAnchor(
  container: HTMLElement,
  scrollTop: number
): ScrollAnchor | null {
  const viewportCenter = scrollTop + container.clientHeight / 2;
  
  // 查找视口中心最近的元素
  const elements = container.querySelectorAll('[data-segment-id]');
  let closestElement: HTMLElement | null = null;
  let minDistance = Infinity;
  
  elements.forEach(el => {
    const rect = el.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();
    const elementCenter = rect.top - containerRect.top + rect.height / 2;
    const distance = Math.abs(elementCenter - viewportCenter);
    
    if (distance < minDistance) {
      minDistance = distance;
      closestElement = el as HTMLElement;
    }
  });
  
  if (!closestElement) return null;
  
  const segmentId = closestElement.getAttribute('data-segment-id')!;
  const rect = closestElement.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();
  const elementTop = rect.top - containerRect.top;
  const offsetPercent = Math.max(0, Math.min(1, 
    (viewportCenter - elementTop) / rect.height
  ));
  
  // 检查是否是标题
  const heading = closestElement.querySelector('h1, h2, h3, h4, h5, h6');
  
  return {
    type: heading ? 'heading' : 'paragraph',
    elementId: heading?.id,
    segmentId,
    offsetPercent
  };
  }
  
export function restoreScrollAnchor(
  container: HTMLElement,
  anchor: ScrollAnchor
): void {
  let targetElement: HTMLElement | null = null;
  
  if (anchor.elementId) {
    targetElement = container.querySelector(`#${anchor.elementId}`);
  }
  
  if (!targetElement) {
    targetElement = container.querySelector(`[data-segment-id="${anchor.segmentId}"]`);
  }
  
  if (targetElement) {
    const rect = targetElement.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();
    const elementTop = rect.top - containerRect.top + container.scrollTop;
    const targetScrollTop = elementTop + rect.height * anchor.offsetPercent - container.clientHeight / 2;
    
    container.scrollTo({
      top: Math.max(0, targetScrollTop),
      behavior: 'auto'
    });
  }
}
```

**3. 自适应防抖**

```typescript
// 修改：components/editor/CodeMirrorEditor.tsx
const calculateAdaptiveDebounce = useCallback((content: string) => {
  const lines = content.split('\n').length;
  return Math.min(Math.max(80 + 0.004 * lines, 80), 200);
}, []);

const debouncedOnChange = useMemo(() => {
  const debounceTime = calculateAdaptiveDebounce(value);
  
  return debounce((newValue: string) => {
    requestAnimationFrame(() => {
      onChange(newValue);
    });
  }, debounceTime, {
    leading: false,
    trailing: true
  });
}, [value, calculateAdaptiveDebounce, onChange]);

// 大段操作检测
const checkForLargeOperation = useCallback((changes: ChangeDesc) => {
  let totalChangeSize = 0;
  changes.iterChanges((fromA, toA, fromB, toB) => {
    totalChangeSize += Math.abs(toB - fromB) + Math.abs(toA - fromA);
  });
  
  // 如果变更超过阈值，绕过防抖
  if (totalChangeSize > 200) { // 200字符或行
    return true;
  }
  return false;
}, []);
```

**4. 一次产出TOC**

```typescript
// 修改：lib/markdown-parser.ts
export interface ParseResult {
  segments: ContentSegment[];
  toc: TOCItem[];
  metadata: {
    wordCount: number;
    readingTime: number;
  };
}

export function parseMarkdownWithTOC(content: string): ParseResult {
  const segments = splitContent(content);
  const toc: TOCItem[] = [];
  let wordCount = 0;
  
  segments.forEach(segment => {
    const headingMatch = segment.content.match(/^(#{1,6})\s+(.+)$/m);
    if (headingMatch) {
      const level = headingMatch[1].length;
      const text = headingMatch[2];
      const id = text.toLowerCase()
        .replace(/[^\w\u4e00-\u9fa5\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
      
      toc.push({
        id,
        text,
        level,
        segmentId: segment.id
      });
    }
    
    wordCount += segment.content.length;
  });
  
  return {
    segments,
    toc,
    metadata: {
      wordCount,
      readingTime: Math.ceil(wordCount / 200) // 200字/分钟
    }
  };
}
```

#### M2 验收标准

- [ ] M档文档输入延迟 < 80ms
- [ ] 滚动锚点恢复准确率 > 95%
- [ ] 长任务 < 60ms
- [ ] 各档位防抖时间自适应生效

### Milestone 3（T+1-2周）- 深度优化

#### 目标指标
- L档文档流畅编辑
- 内存稳定（2分钟不增长>10%）
- DOM节点 ≤ 2500

#### 核心任务

**1. Web Worker解析管线**

```typescript
// 新增：worker/markdown-worker.ts
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeStringify from 'rehype-stringify';

interface WorkerRequest {
  type: 'parse';
  segments: Array<{
    id: string;
    content: string;
  }>;
  rev: number; // 版本号
}

interface WorkerResponse {
  type: 'parse-result';
  results: Array<{
    id: string;
    html: string;
    metadata?: any;
  }>;
  rev: number;
}

const processor = unified()
  .use(remarkParse)
  .use(remarkGfm)
  .use(remarkRehype, { allowDangerousHtml: false })
  .use(rehypeStringify);

self.onmessage = (event: MessageEvent<WorkerRequest>) => {
  const { segments, rev } = event.data;
  
  try {
    const results = segments.map(segment => {
      const result = processor.processSync(segment.content);
      return {
        id: segment.id,
        html: String(result),
        metadata: extractMetadata(segment.content)
      };
    });
    
    const response: WorkerResponse = {
      type: 'parse-result',
      results,
      rev
    };
    
    self.postMessage(response);
  } catch (error) {
    self.postMessage({
      type: 'parse-error',
      error: error.message,
      rev
    });
  }
};

function extractMetadata(content: string) {
  const headingMatch = content.match(/^(#{1,6})\s+(.+)$/m);
  if (headingMatch) {
    return {
      type: 'heading',
      level: headingMatch[1].length,
      text: headingMatch[2]
    };
  }
  return { type: 'content' };
}
```

**2. Worker通信协议**

```typescript
// 新增：lib/worker-manager.ts
export class WorkerManager {
  private worker: Worker | null = null;
  private currentRev = 0;
  private pendingCallbacks = new Map<number, (result: any) => void>();
  private fallbackMode = false;

  constructor() {
    this.initWorker();
  }

  private initWorker() {
    try {
      this.worker = new Worker('/worker/markdown-worker.js');
      this.worker.onmessage = this.handleWorkerMessage.bind(this);
      this.worker.onerror = this.handleWorkerError.bind(this);
    } catch (error) {
      console.warn('Worker initialization failed, using fallback:', error);
      this.fallbackMode = true;
    }
  }

  async parseSegments(segments: ContentSegment[]): Promise<ParsedSegment[]> {
    if (this.fallbackMode || !this.worker) {
      // 降级到主线程解析
      return this.parseSegmentsMainThread(segments);
    }

    return new Promise((resolve) => {
      const rev = ++this.currentRev;
      this.pendingCallbacks.set(rev, resolve);
      
      this.worker!.postMessage({
        type: 'parse',
        segments: segments.map(seg => ({
          id: seg.id,
          content: seg.content
        })),
        rev
      });
      
      // 超时降级
      setTimeout(() => {
        if (this.pendingCallbacks.has(rev)) {
          this.pendingCallbacks.delete(rev);
          console.warn('Worker timeout, using fallback');
          resolve(this.parseSegmentsMainThread(segments));
        }
      }, 5000);
    });
  }

  private handleWorkerMessage(event: MessageEvent) {
    const { rev, results, type } = event.data;
    
    if (type === 'parse-result' && this.pendingCallbacks.has(rev)) {
      const callback = this.pendingCallbacks.get(rev)!;
      this.pendingCallbacks.delete(rev);
      callback(results);
    }
  }

  private handleWorkerError(error: ErrorEvent) {
    console.error('Worker error:', error);
    this.fallbackMode = true;
  }

  private parseSegmentsMainThread(segments: ContentSegment[]): ParsedSegment[] {
    // 主线程降级解析，增强防抖
    return segments.map(segment => ({
      id: segment.id,
      html: renderSegment(segment.content),
      metadata: {}
    }));
  }

  destroy() {
    this.worker?.terminate();
    this.pendingCallbacks.clear();
  }
}
```

**3. 视口内高亮优化**

```typescript
// 新增：lib/viewport-highlighter.ts
export class ViewportHighlighter {
  private observer: IntersectionObserver;
  private pendingHighlights = new Set<HTMLElement>();

  constructor(container: HTMLElement) {
    this.observer = new IntersectionObserver(
      this.handleIntersection.bind(this),
      {
        root: container,
        rootMargin: '200px', // 提前200px开始高亮
        threshold: 0.1
      }
    );
  }

  observeCodeBlocks(container: HTMLElement) {
    const codeBlocks = container.querySelectorAll('pre code:not(.hljs)');
    codeBlocks.forEach(block => {
      this.observer.observe(block as HTMLElement);
    });
  }

  private handleIntersection(entries: IntersectionObserverEntry[]) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const codeBlock = entry.target as HTMLElement;
        this.highlightCodeBlock(codeBlock);
        this.observer.unobserve(codeBlock);
      }
    });
  }

  private async highlightCodeBlock(codeBlock: HTMLElement) {
    if (this.pendingHighlights.has(codeBlock)) return;
    this.pendingHighlights.add(codeBlock);

    try {
      // 动态导入highlight.js
      const { default: hljs } = await import('highlight.js');
      hljs.highlightElement(codeBlock);
      codeBlock.classList.add('hljs');
    } catch (error) {
      console.error('Code highlighting failed:', error);
    } finally {
      this.pendingHighlights.delete(codeBlock);
    }
  }

  destroy() {
    this.observer.disconnect();
    this.pendingHighlights.clear();
  }
}
```

**4. 数据驱动虚拟滚动**

```typescript
// 新增：components/VirtualScrollRenderer.tsx
interface VirtualScrollProps {
  segments: ContentSegment[];
  containerHeight: number;
  enableCondition: () => boolean;
}

export function VirtualScrollRenderer({ segments, containerHeight, enableCondition }: VirtualScrollProps) {
  const [shouldUseVirtual, setShouldUseVirtual] = useState(false);
  const [itemHeights, setItemHeights] = useState<Map<string, number>>(new Map());

  // 检查启用条件
  useEffect(() => {
    const domNodeCount = document.querySelectorAll('*').length;
    const segmentCount = segments.length;
    
    // 启用条件：段落数 > 3000 或 DOM节点 > 2500
    if (segmentCount > 3000 || domNodeCount > 2500) {
      setShouldUseVirtual(true);
    }
  }, [segments.length]);

  if (!shouldUseVirtual) {
    // 使用原有渲染方式
    return <NormalRenderer segments={segments} />;
  }

  // 实现虚拟滚动逻辑
  return (
    <VirtualList
      items={segments}
      itemHeight={(index) => itemHeights.get(segments[index].id) || 100}
      overscan={2} // ±2屏预渲染
      renderItem={({ item, style }) => (
        <div style={style} data-segment-id={item.id}>
          {renderSegment(item.content)}
        </div>
      )}
    />
  );
}
```

#### M3 验收标准

- [ ] L档文档输入延迟 < 100ms
- [ ] 内存使用稳定（连续2分钟测试）
- [ ] DOM节点 ≤ 2500
- [ ] 虚拟滚动无功能回归

## 🔧 实施计划

### 特性开关配置

```typescript
// 新增：lib/feature-flags.ts
export const FEATURE_FLAGS = {
  STABLE_SEGMENT_ID: true,    // M1
  KEYED_DIFF: true,           // M1
  DEBOUNCED_INPUT: true,      // M1
  INCREMENTAL_PARSING: false, // M2
  SEMANTIC_SCROLL: false,     // M2
  ADAPTIVE_DEBOUNCE: false,   // M2
  WORKER_PARSING: false,      // M3
  VIRTUAL_SCROLLING: false,   // M3
  VIEWPORT_HIGHLIGHTING: false // M3
};

export function isFeatureEnabled(feature: keyof typeof FEATURE_FLAGS): boolean {
  return FEATURE_FLAGS[feature];
}
```

### 测试计划

```typescript
// 新增：tests/performance.spec.ts
describe('Performance Regression Tests', () => {
  const testDocuments = {
    S: '2-3k行文档',
    M: '8-10k行文档', 
    L: '18-25k行文档'
  };

  test('M1 - 输入延迟测试', async () => {
    const result = await measureInputLatency(testDocuments.S);
    expect(result.p95).toBeLessThan(150);
});

  test('M1 - 滚动FPS测试', async () => {
    const fps = await measureScrollFPS(testDocuments.S, 5000);
    expect(fps).toBeGreaterThanOrEqual(55);
});

  test('M1 - 长任务检测', async () => {
    const longTasks = await measureLongTasks(testDocuments.S);
    expect(Math.max(...longTasks)).toBeLessThan(50);
  });

  test('M2 - 滚动锚点稳定性', async () => {
    const accuracy = await testScrollAnchorAccuracy();
    expect(accuracy).toBeGreaterThan(0.95);
});

  test('M3 - 内存稳定性', async () => {
    const memoryGrowth = await testMemoryStability(testDocuments.L, 120000);
    expect(memoryGrowth).toBeLessThan(0.1); // 小于10%
  });
});
```

### 风险控制

**回退机制**
```typescript
// 每个优化都有即时回退能力
if (!isFeatureEnabled('WORKER_PARSING') || workerError) {
  // 降级到主线程解析
  useMainThreadParsing();
}

if (!isFeatureEnabled('VIRTUAL_SCROLLING') || virtualScrollError) {
  // 降级到分批加载
  useProgressiveMounting();
}
```

**监控告警**
```typescript
// 性能指标监控
const monitor = {
  longTaskThreshold: 50,
  inputLatencyThreshold: 150,
  memoryGrowthThreshold: 0.1
};

// 自动降级触发
if (detectPerformanceRegression(monitor)) {
  rollbackToStableVersion();
}
```

## 📅 时间线

| 阶段 | 时间 | 主要任务 | 交付物 |
|------|------|----------|---------|
| M1 | T+2天 | 稳定ID + 防抖 + keyed-diff | 可验证的性能提升 |
| M2 | T+1周 | 增量解析 + 语义滚动 | 结构性能优化 |
| M3 | T+2周 | Worker + 虚拟滚动 | 深度性能优化 |

## ✅ 成功标准

**最终验收**：
- ✅ 滚动 ≥ 55 FPS
- ✅ 输入延迟 P95 < 150ms  
- ✅ 长任务 < 50ms
- ✅ DOM节点 ≤ 2500
- ✅ L档2分钟内存增长 < 10%
- ✅ 所有现有功能无回归

**实施承诺**：严格按照专家指导和本方案执行，确保性能优化成功完成。
