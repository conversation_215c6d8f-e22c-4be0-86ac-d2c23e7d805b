# Cursor 对专家指导的第一次回复

## 📋 专家指导确认

感谢专家的详细分析！我们完全认同您的诊断：

### ✅ 问题确认准确
专家指出的6个核心问题与我们分析完全一致：
1. **无内部优化库** - 仍用同步 unified → rehype
2. **全量分割渲染** - 每次 content 改变都重新 `split` + `renderMarkdown`
3. **同步渲染阻塞** - `processor.processSync` 阻塞主线程
4. **编辑触发重建** - `finishEditing()` 拼接全部段落
5. **事件监听重绑** - 每改一次 content 就重绑 + 行号 O(n²) 搜索
6. **性能优化未接入** - 监控/防抖/增量解析全未接入

### 🎯 架构升级方向确认
完全同意需要将实时模式升级到与 M2 同级的"块-Worker-虚拟化"架构。

---

## 📋 专家要求的资料补充

### 1. **增量解析 API 现状**

```typescript
// lib/incremental-parser.ts 当前返回结构
export interface IncrementalParseResult {
  changes: SegmentChange[];
  affectedRange: { start: number; end: number };
  newBoundaryIndex: BoundaryIndex;
}

export interface SegmentChange {
  type: 'add' | 'remove' | 'update' | 'unchanged';
  segmentId: string;
  content: string;
  startOffset: number;
  endOffset: number;
}
```

**问题**：当前 `parseIncremental()` 在 LiveRenderMode 中**完全未使用**，只在 M2 渲染器中调用。

**受影响行定位算法**：
```typescript
private calculateAffectedRange(changes: CodeMirrorChangeDesc): { start: number; end: number } {
  // 基于 CodeMirror 变更描述计算影响范围
  // 但 LiveRenderMode 没有 CodeMirror 变更信息
}
```

### 2. **Worker 构建方式**

**当前构建配置**：
```javascript
// next.config.js
const nextConfig = {
  eslint: { ignoreDuringBuilds: true },
  images: { unoptimized: true },
};
```

**问题**：
- 未配置 Worker 支持
- 需要确认 Next.js 13 的 Worker 构建方式
- 是否需要 webpack 5 的 Worker 配置？

### 3. **虚拟列表库计划**

**当前状态**：无虚拟列表实现

**计划方案**：
- 优先考虑 `react-window`（轻量级）
- 备选 `@tanstack/react-virtual`（功能更丰富）
- 自研方案（如果性能要求极高）

**高度计算策略**：
```typescript
// 预估高度计算
const estimateParagraphHeight = (markdown: string): number => {
  const lines = markdown.split('\n').length;
  const hasCodeBlock = markdown.includes('```');
  const hasTable = markdown.includes('|');
  
  return Math.max(60, lines * 20 + (hasCodeBlock ? 40 : 0) + (hasTable ? 30 : 0));
};
```

### 4. **协作方案**

**当前状态**：无协作功能

**未来计划**：
- 优先考虑 OT（Operational Transformation）
- 使用 Y.js 或 ShareDB
- Block key 策略：`hash32(content) + ':' + startOffset`

---

## 🚀 P0 快修排期确认（7天）

### Day 1-2: 接入 AdaptiveDebounce
- [ ] 在 LiveRenderMode 中集成 AdaptiveDebounceManager
- [ ] 实现 `onChange → debounceManager.getDelay()` 调用
- [ ] 测试防抖效果

### Day 3-4: Markdown 渲染 Worker
- [ ] 创建 Markdown 渲染 Worker
- [ ] 实现 `await worker.renderBatch(changedParagraphs)`
- [ ] 测试 Worker 渲染性能

### Day 5-6: 段落级缓存
- [ ] 实现 `WeakMap<markdown, html>` 缓存
- [ ] 集成 hash 计算
- [ ] 测试缓存命中率

### Day 7: React.memo 优化
- [ ] ParagraphBlock 添加 React.memo
- [ ] key 改为 `paragraph.hash`
- [ ] 性能测试和调优

---

## ❓ 技术实现疑问

### 1. **Worker 构建配置**
```javascript
// 需要确认的配置
module.exports = {
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }
    return config;
  },
};
```

**问题**：这个配置是否正确？是否需要其他 Worker 相关配置？

### 2. **增量解析接入方式**
```typescript
// 当前 LiveRenderMode 没有 CodeMirror 变更信息
// 如何获取变更信息？
const handleContentChange = (newContent: string) => {
  // 这里只有新内容，没有变更描述
  // 如何调用 parseIncremental？
};
```

**问题**：如何在没有 CodeMirror 变更信息的情况下使用增量解析？

### 3. **缓存 Hash 策略**
```typescript
// 建议的 Hash 策略
const generateParagraphHash = (markdown: string): string => {
  return fastHash32(markdown.trim());
};
```

**问题**：这个 Hash 策略是否足够稳定？是否需要考虑段落位置？

---

## 📋 请求专家指导

### 1. **Worker 构建配置**
请提供完整的 Next.js 13 Worker 构建配置示例。

### 2. **增量解析接入**
请提供在没有 CodeMirror 变更信息的情况下，如何实现增量解析的具体代码示例。

### 3. **虚拟列表实现**
请提供 react-window 在实时模式中的具体集成示例。

### 4. **性能测试基准**
请提供具体的性能测试用例和基准数据。

---

**我们准备立即开始 P0 快修，请专家提供上述技术实现的具体代码示例。** 