# 技术资料回复 - 专家要求的4项信息

## 📋 回复专家第一次指导的技术需求

根据专家要求，现提供以下4项关键技术信息：

---

## 1️⃣ 增量解析 API 详细信息

### 当前 `parseIncremental()` 返回结构

**位置**: `lib/incremental-parser.ts:116-201`

```typescript
export interface SegmentChange {
  type: 'add' | 'remove' | 'update' | 'unchanged';
  segmentId: string;
  content: string;
  startOffset: number;
  endOffset: number;
}

export interface IncrementalParseResult {
  changes: SegmentChange[];
  affectedRange: { start: number; end: number };
  newBoundaryIndex: BoundaryIndex;
}

// 核心方法
parseIncremental(newContent: string, changes: CodeMirrorChangeDesc): IncrementalParseResult
```

### 受影响行定位算法

**当前实现**:
```typescript
private calculateAffectedRange(changes: CodeMirrorChangeDesc): { start: number; end: number } {
  let minStart = Infinity;
  let maxEnd = 0;

  changes.iterChanges((fromA: number, toA: number, fromB: number, toB: number) => {
    minStart = Math.min(minStart, fromA);
    maxEnd = Math.max(maxEnd, toB);
  });

  return {
    start: minStart === Infinity ? 0 : minStart,
    end: maxEnd
  };
}
```

### 段落ID生成策略

**当前实现**: `generateStableSegmentId(content, startOffset)`
```typescript
// lib/hash-utils.ts
export function generateStableSegmentId(content: string, startOffset: number): string {
  const hash = simpleHash(content);
  return `${hash}:${startOffset}`;
}
```

**✅ 复用性评估**: 完全可以复用，已经实现了专家要求的 `{added, updated, removed}` 块数组返回。

---

## 2️⃣ Worker 构建方式详细信息

### 当前构建配置

**Next.js 配置**: `next.config.js`
```javascript
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: { unoptimized: true },
};
```

**TypeScript 配置**: `tsconfig.json`
```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "module": "esnext",
    "moduleResolution": "bundler",
    "isolatedModules": true,
    "jsx": "preserve"
  }
}
```

### Worker 支持能力

**✅ 支持情况**:
- Next.js 13+ 原生支持 Web Worker
- 可以使用 `new Worker('/worker/markdown-worker.js')`
- 支持 ESM 模块导入
- 支持多线程 Worker Pool

**🔧 需要的配置调整**:
```javascript
// next.config.js 需要添加
const nextConfig = {
  webpack: (config) => {
    config.module.rules.push({
      test: /\.worker\.ts$/,
      use: {
        loader: 'worker-loader',
        options: {
          name: 'static/[hash].worker.js',
          publicPath: '/_next/',
        },
      },
    });
    return config;
  },
};
```

**📦 打包方式**: 
- 标准 Webpack Worker 打包
- 支持多 Worker 实例
- 可以实现 Worker Pool 架构

---

## 3️⃣ 虚拟列表库计划

### 技术选型倾向

**首选方案**: `@tanstack/react-virtual`
- ✅ 轻量级 (< 10KB)
- ✅ TypeScript 原生支持
- ✅ 动态高度支持
- ✅ 水平/垂直滚动
- ✅ 活跃维护

**备选方案**: `react-window`
- ✅ 成熟稳定
- ⚠️ 动态高度支持较弱
- ✅ 生态丰富

### 高度计算策略

**预计实现方案**:
```typescript
// 段落高度估算
const estimateItemHeight = (segment: ContentSegment): number => {
  const baseHeight = 24; // 基础行高
  const lines = segment.content.split('\n').length;
  
  // 根据内容类型调整
  if (segment.content.startsWith('#')) {
    return baseHeight * 1.5 * lines; // 标题
  } else if (segment.content.startsWith('```')) {
    return baseHeight * 1.2 * lines; // 代码块
  } else {
    return baseHeight * lines; // 普通段落
  }
};

// 动态高度更新
const updateItemHeight = (index: number, element: HTMLElement) => {
  const actualHeight = element.getBoundingClientRect().height;
  virtualizer.measureElement(index, actualHeight);
};
```

**启用条件**:
```typescript
// 专家建议的启用条件
const shouldEnableVirtualScrolling = () => {
  return segmentCount > 3000 || 
         (domNodes > 2500 && fps < 50);
};
```

---

## 4️⃣ 协作方案信息

### 当前状态

**❌ 暂无多人实时协作计划**
- 当前版本为单用户编辑器
- 未来可能考虑协作功能
- 暂时不需要 OT/CRDT 方案

### 如果未来需要协作

**Block key 设计倾向**:
```typescript
// 稳定的段落标识
interface BlockKey {
  hash: string;        // 内容哈希
  startOffset: number; // 文档位置
  timestamp: number;   // 创建时间
  userId?: string;     // 创建用户(协作时)
}

// merge 规则倾向
const mergeStrategy = {
  conflictResolution: 'last-write-wins', // 简单策略
  blockGranularity: 'paragraph',         // 段落级别
  preserveIntentions: true               // 保持用户意图
};
```

**但当前优先级**: 🔴 **不考虑协作**，专注性能优化。

---

## 🎯 P0 快修排期确认

### 7天实施计划

| 天数 | 任务 | 负责人 | 状态 |
|------|------|--------|------|
| Day 1-2 | 接入 AdaptiveDebounce | 开发者A | ✅ 准备就绪 |
| Day 3-4 | 段落级缓存 + React.memo | 开发者A | ✅ 准备就绪 |
| Day 5-6 | Worker 渲染基础版 | 开发者B | ✅ 准备就绪 |
| Day 7 | 性能监控接入 + 测试 | 开发者A+B | ✅ 准备就绪 |

### 技术准备状态

**✅ 已准备就绪**:
- AdaptiveDebounceManager 可直接调用
- PerformanceMonitor 可直接接入
- IncrementalParser 可直接使用
- Worker 构建环境已确认

**🔧 需要专家指导**:
- Worker 渲染的具体实现示例
- 段落缓存的最佳策略 (WeakMap vs Map)
- React.memo 的比较函数优化

---

## 📞 请求专家下一步指导

基于以上技术信息，我们已经确认：

1. ✅ **增量解析 API** - 完全可复用现有实现
2. ✅ **Worker 构建** - Next.js 支持，可实现 Worker Pool
3. ✅ **虚拟列表** - 倾向 @tanstack/react-virtual，已有高度计算策略
4. ✅ **协作方案** - 暂不考虑，专注性能优化

**请专家提供**:
1. **逐文件改动示例 (diff patch)**
2. **Worker 渲染骨架代码**
3. **虚拟列表封装示例**

我们已经准备好按照专家的具体指导开始 P0 快修实施！

---

**开发团队状态**: 🚀 **完全就绪，等待专家的详细实施指导**
