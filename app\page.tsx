'use client';

import React, { useState, useEffect, useRef } from 'react';
import { TopBar } from '@/components/layout/TopBar';
import { LeftSidebar } from '@/components/layout/LeftSidebar';
import { RightSidebar } from '@/components/layout/RightSidebar';
import { StatusBar } from '@/components/layout/StatusBar';
import { EditorArea } from '@/components/editor/EditorArea';
import { CommitDetailView } from '@/components/git/CommitDetailView';

import { ThemeProvider, useTheme } from '@/components/providers/ThemeProvider';
import { SessionProvider } from '@/components/providers/SessionProvider';
import { useAppState } from '@/lib/storage';

function AppContent() {
  const { theme, toggleTheme } = useTheme();

  // 添加客户端挂载状态
  const [mounted, setMounted] = useState(false);

  // 使用新的状态管理Hook
  const { state, updateState, saveScrollPosition } = useAppState({});

  // 从状态中解构需要的值
  const {
    currentFile,
    isLeftSidebarCollapsed,
    isRightSidebarOpen,
    isViewingCommit,
    content,
    leftSidebarActiveTab
  } = state;

  // 保留一些本地状态
  const [isSaved, setIsSaved] = useState(true);
  const [currentBranch, setCurrentBranch] = useState('main');
  const [gitStatusVersion, setGitStatusVersion] = useState(0); // 添加版本控制来触发Git面板刷新
  const [commitContent, setCommitContent] = useState<string>('');
  const [commitFilename, setCommitFilename] = useState<string>('');

  // 使用useRef确保获取到最新的currentFile值
  const currentFileRef = useRef<string | null>(null);

  // 客户端挂载检测
  useEffect(() => {
    setMounted(true);
  }, []);

  // 强制刷新Git状态
  const refreshGitStatus = () => {
    setGitStatusVersion(prev => prev + 1);
  };

  // 同步currentFile到ref
  useEffect(() => {
    currentFileRef.current = currentFile;
    console.log(`📌 更新currentFileRef: ${currentFile}`);
  }, [currentFile]);

  // 初始化时加载默认文件
  useEffect(() => {
    const loadDefaultFile = async () => {
      try {
        const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();
        const files = gitService.getFileList();

        // 如果没有当前文件且有文件列表，加载默认文件
        if (files.length > 0 && !currentFile) {
          const defaultFile = files.find(f => f === 'README.md') || files[0];
          console.log(`初始化加载默认文件: ${defaultFile}`);

          const fileContent = gitService.getFileContent(defaultFile);
          updateState({
            currentFile: defaultFile,
            content: fileContent || ''
          });
        }
        // 如果有保存的当前文件，验证文件是否仍然存在
        else if (currentFile && !files.includes(currentFile)) {
          console.log(`保存的文件 ${currentFile} 不存在，加载默认文件`);
          const defaultFile = files.find(f => f === 'README.md') || files[0];
          if (defaultFile) {
            const fileContent = gitService.getFileContent(defaultFile);
            updateState({
              currentFile: defaultFile,
              content: fileContent || ''
            });
          }
        }
        // 如果有保存的当前文件且文件存在，确保内容是最新的
        else if (currentFile && files.includes(currentFile)) {
          const fileContent = gitService.getFileContent(currentFile);
          if (fileContent !== content) {
            console.log(`更新文件内容: ${currentFile}`);
            updateState({ content: fileContent || '' });
          }
        }
      } catch (error) {
        console.error('加载默认文件失败:', error);
      }
    };

    loadDefaultFile();
  }, []); // 只在组件挂载时执行一次

  // 处理提交内容变化
  const handleCommitContentChange = (content: string, filename: string) => {
    setCommitContent(content);
    setCommitFilename(filename);
    updateState({ isViewingCommit: true });
  };

  // 处理文件选择
  const handleFileSelect = async (filepath: string) => {
    try {
      console.log(`=== 文件选择事件 ===`);
      console.log(`- 选择的文件: ${filepath}`);
      console.log(`- 当前文件: ${currentFile}`);

      // 先保存当前编辑的内容（如果有的话）
      if (currentFileRef.current && content && currentFileRef.current !== filepath) {
        console.log(`💾 选择新文件前，先保存当前文件: ${currentFileRef.current}`);
        const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();
        gitService.updateFile(currentFileRef.current, content);
        console.log(`- 保存的内容长度: ${content.length}`);
      }

      // 从Git服务获取文件内容
      const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();
      const fileContent = gitService.getFileContent(filepath);

      console.log(`- 获取到的内容长度: ${fileContent?.length || 0}`);
      console.log(`- 内容预览: "${fileContent?.substring(0, 50) || ''}..."`);

      // 更新状态
      updateState({
        currentFile: filepath,
        content: fileContent || '',
        isViewingCommit: false
      });

      setIsSaved(true);

      console.log(`- 文件选择完成: ${filepath}`);
    } catch (error) {
      console.error('加载文件失败:', error);
      updateState({
        content: `# ${filepath}\n\n文件加载失败，请重试。`
      });
    }
  };

  // 处理内容保存
  const handleContentSave = async () => {
    const actualCurrentFile = currentFileRef.current;
    if (!actualCurrentFile) return;

    try {
      const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();
      gitService.updateFile(actualCurrentFile, content);
      setIsSaved(true);
      console.log(`文件已保存到Git服务: ${actualCurrentFile}, 内容长度: ${content.length}`);
      refreshGitStatus(); // 保存成功后刷新Git状态
    } catch (error) {
      console.error('保存文件失败:', error);
    }
  };

  return (
    <div className="h-screen flex flex-col bg-background text-foreground">
      {/* Top Bar */}
      <TopBar
        isDarkMode={theme === 'dark'}
        onThemeToggle={toggleTheme}
        isSaved={isSaved}
        content={content}
        onSave={handleContentSave}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden main-content-area">
        {/* Left Sidebar */}
        <LeftSidebar
          isCollapsed={isLeftSidebarCollapsed}
          onToggle={() => updateState({ isLeftSidebarCollapsed: !isLeftSidebarCollapsed })}
          onFileSelect={handleFileSelect}
          currentFile={currentFile}
          currentFileContent={content}
          gitStatusVersion={gitStatusVersion}
          onCommitContentChange={handleCommitContentChange}
          activeTab={leftSidebarActiveTab || 'outline'}
          onActiveTabChange={(tab) => updateState({ leftSidebarActiveTab: tab })}
        />

        {/* Editor Area or Commit Detail View */}
        <div className="flex-1 min-w-0 flex flex-col editor-content-wrapper">
          {isViewingCommit ? (
            <CommitDetailView
              commitData={commitContent}
              filename={commitFilename}
              onFileSelect={handleFileSelect}
            />
          ) : (
            <EditorArea
              onSettingsToggle={() => updateState({ isRightSidebarOpen: !isRightSidebarOpen })}
              content={content}
              currentFile={currentFile}
              initialScrollPosition={state.scrollPosition}
              onScrollPositionChange={saveScrollPosition}
            onContentChange={async (newContent, editorCurrentFile) => {
              console.log(`=== 编辑器内容变化 ===`);
              console.log(`- 状态中的currentFile: ${currentFile}`);
              console.log(`- Ref中的currentFile: ${currentFileRef.current}`);
              console.log(`- 编辑器传递的currentFile: ${editorCurrentFile}`);
              console.log(`- 新内容长度: ${newContent.length}`);
              console.log(`- 新内容预览: "${newContent.substring(0, 50)}..."`);
              console.log(`- 之前内容长度: ${content.length}`);

              // 更新状态
              updateState({ content: newContent });
              setIsSaved(false);

              // 使用编辑器传递的文件名，这是最可靠的
              const targetFile = editorCurrentFile || currentFileRef.current;

              console.log(`- 最终使用的目标文件: ${targetFile}`);

              // 自动同步到Git服务
              if (targetFile) {
                try {
                  const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();

                  // 检查同步前的状态
                  const beforeContent = gitService.getFileContent(targetFile);
                  console.log(`- 同步前Git中的内容长度: ${beforeContent?.length || 0}`);

                  console.log(`- 同步内容到文件: ${targetFile}`);
                  gitService.updateFile(targetFile, newContent);

                  // 检查同步后的状态
                  const afterContent = gitService.getFileContent(targetFile);
                  console.log(`- 同步后Git中的内容长度: ${afterContent?.length || 0}`);

                  console.log(`✅ 内容已同步到Git服务: ${targetFile}, 长度: ${newContent.length}`);

                  // 🔧 确保立即刷新Git状态，无需手动保存
                  console.log(`🔄 自动刷新Git状态...`);
                  refreshGitStatus();
                } catch (error) {
                  console.error('❌ 同步到Git服务失败:', error);
                }
              } else {
                console.warn('⚠️ 无法确定目标文件，无法同步内容');
              }
            }}
            />
          )}
        </div>

        {/* Right Sidebar */}
        <RightSidebar
          isOpen={isRightSidebarOpen}
          onClose={() => updateState({ isRightSidebarOpen: false })}
        />
      </div>

      {/* Status Bar */}
      {mounted && (
        <StatusBar
          wordCount={content ? content.split(/\s+/).filter(word => word.length > 0).length : 0}
          currentBranch={currentBranch}
          lastSaved="2 分钟前"
        />
      )}

    </div>
  );
}

export default function Home() {
  return (
    <SessionProvider>
      <ThemeProvider>
        <AppContent />
      </ThemeProvider>
    </SessionProvider>
  );
}