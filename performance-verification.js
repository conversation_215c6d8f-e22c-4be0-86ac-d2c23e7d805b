// 🔍 虚拟滚动性能验证脚本
// 根据专家要求验证关键指标

console.log('🚀 开始虚拟滚动性能验证...');

// 1. 验证DOM节点数 (目标: ≤35)
function verifyDOMNodes() {
  const virtualParent = document.getElementById('virtual-parent');
  if (!virtualParent) {
    console.error('❌ 未找到虚拟滚动容器 #virtual-parent');
    return false;
  }
  
  const domNodeCount = virtualParent.querySelectorAll('*').length;
  const isOptimal = domNodeCount <= 35;
  
  console.log('📊 DOM节点验证:', {
    当前节点数: domNodeCount,
    目标节点数: '≤35',
    验证结果: isOptimal ? '✅ 通过' : '❌ 失败',
    优化程度: domNodeCount > 2500 ? '未优化' : `优化了 ${Math.round((1 - domNodeCount/2500) * 100)}%`
  });
  
  return isOptimal;
}

// 2. 验证虚拟滚动是否生效
function verifyVirtualScrolling() {
  const virtualParent = document.getElementById('virtual-parent');
  if (!virtualParent) return false;
  
  // 检查是否有虚拟滚动的特征
  const hasVirtualItems = virtualParent.querySelector('[style*="transform: translateY"]');
  const hasAbsolutePositioning = virtualParent.querySelector('[style*="position: absolute"]');
  
  const isWorking = hasVirtualItems && hasAbsolutePositioning;
  
  console.log('🔄 虚拟滚动验证:', {
    虚拟项目: hasVirtualItems ? '✅ 发现' : '❌ 未发现',
    绝对定位: hasAbsolutePositioning ? '✅ 发现' : '❌ 未发现',
    虚拟滚动状态: isWorking ? '✅ 正常工作' : '❌ 未生效'
  });
  
  return isWorking;
}

// 3. 测量输入响应时间 (目标: <150ms)
function measureInputResponse() {
  return new Promise((resolve) => {
    const startTime = performance.now();
    
    // 模拟输入事件
    const testInput = () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      const isOptimal = duration < 150;
      
      console.log('⚡ 输入响应验证:', {
        响应时间: `${duration.toFixed(2)}ms`,
        目标时间: '<150ms',
        验证结果: isOptimal ? '✅ 通过' : '❌ 超时'
      });
      
      resolve({ duration, isOptimal });
    };
    
    // 使用 requestAnimationFrame 来测量渲染响应
    requestAnimationFrame(testInput);
  });
}

// 4. 测量FPS (目标: ≥55)
function measureFPS() {
  return new Promise((resolve) => {
    let frames = 0;
    let startTime = performance.now();
    
    function countFrames() {
      frames++;
      const currentTime = performance.now();
      
      if (currentTime - startTime >= 1000) {
        const fps = frames;
        const isOptimal = fps >= 55;
        
        console.log('🎯 FPS验证:', {
          当前FPS: fps,
          目标FPS: '≥55',
          验证结果: isOptimal ? '✅ 通过' : '❌ 低于目标'
        });
        
        resolve({ fps, isOptimal });
      } else {
        requestAnimationFrame(countFrames);
      }
    }
    
    requestAnimationFrame(countFrames);
  });
}

// 5. 检查长任务 (目标: 0个)
function checkLongTasks() {
  return new Promise((resolve) => {
    let longTaskCount = 0;
    
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        longTaskCount += entries.length;
        
        entries.forEach((entry) => {
          console.warn('🚨 检测到长任务:', {
            持续时间: `${entry.duration.toFixed(2)}ms`,
            开始时间: entry.startTime,
            任务名称: entry.name
          });
        });
      });
      
      observer.observe({ entryTypes: ['longtask'] });
      
      // 监控5秒
      setTimeout(() => {
        observer.disconnect();
        
        const isOptimal = longTaskCount === 0;
        
        console.log('⏱️ 长任务验证:', {
          长任务数量: longTaskCount,
          目标数量: '0个',
          验证结果: isOptimal ? '✅ 通过' : '❌ 存在长任务'
        });
        
        resolve({ longTaskCount, isOptimal });
      }, 5000);
    } else {
      console.warn('⚠️ 浏览器不支持 LongTasks API');
      resolve({ longTaskCount: 0, isOptimal: true });
    }
  });
}

// 6. 综合性能验证
async function runFullVerification() {
  console.log('🔍 开始综合性能验证...\n');
  
  const results = {
    domNodes: verifyDOMNodes(),
    virtualScrolling: verifyVirtualScrolling(),
    inputResponse: await measureInputResponse(),
    fps: await measureFPS(),
    longTasks: await checkLongTasks()
  };
  
  console.log('\n📋 验证结果汇总:');
  console.log('==================');
  
  const allPassed = [
    results.domNodes,
    results.virtualScrolling,
    results.inputResponse.isOptimal,
    results.fps.isOptimal,
    results.longTasks.isOptimal
  ].every(Boolean);
  
  console.log(`总体评估: ${allPassed ? '✅ 全部通过' : '❌ 部分失败'}`);
  
  if (allPassed) {
    console.log('🎉 恭喜！虚拟滚动优化完全符合专家要求！');
  } else {
    console.log('⚠️ 部分指标未达标，需要进一步优化。');
  }
  
  return results;
}

// 7. 快速验证函数 (专家要求的简化版本)
function quickVerify() {
  const virtualParent = document.getElementById('virtual-parent');
  const domNodeCount = virtualParent ? virtualParent.querySelectorAll('*').length : 0;
  
  console.log('📊 快速验证结果:', {
    DOM节点数: domNodeCount,
    是否达标: domNodeCount <= 35 ? '✅ 通过' : '❌ 失败',
    虚拟滚动: virtualParent ? '✅ 生效' : '❌ 未生效'
  });
  
  return domNodeCount <= 35;
}

// 导出验证函数
window.verifyOptimization = quickVerify;
window.runFullVerification = runFullVerification;
window.measureInputResponse = measureInputResponse;
window.measureFPS = measureFPS;

console.log('✅ 性能验证脚本已加载');
console.log('💡 使用方法:');
console.log('  - quickVerify() - 快速验证');
console.log('  - runFullVerification() - 完整验证');
console.log('  - verifyOptimization() - 专家要求的验证函数');
