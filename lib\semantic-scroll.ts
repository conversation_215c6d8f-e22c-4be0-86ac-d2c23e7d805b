interface SemanticAnchor {
  type: 'heading' | 'paragraph' | 'code' | 'list';
  id: string;
  level?: number; // for headings
  text: string;
  offset: number; // 在文档中的字符偏移量
  element?: HTMLElement; // DOM元素引用
}

interface ScrollPosition {
  anchor: SemanticAnchor;
  relativeOffset: number; // 相对于锚点的偏移量（0-1之间）
  pixelOffset: number; // 像素偏移量
}

export class SemanticScrollManager {
  private container: HTMLElement;
  private anchors: SemanticAnchor[] = [];
  private lastPosition: ScrollPosition | null = null;
  private isRestoring = false;

  constructor(container: HTMLElement) {
    this.container = container;
    this.setupScrollListener();
  }

  private setupScrollListener(): void {
    let scrollTimeout: NodeJS.Timeout;
    
    this.container.addEventListener('scroll', () => {
      if (this.isRestoring) return;
      
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        this.captureCurrentPosition();
      }, 100); // 100ms 防抖
    });
  }

  /**
   * 扫描容器，建立语义锚点
   */
  scanForAnchors(): void {
    this.anchors = [];
    
    const elements = this.container.querySelectorAll('h1, h2, h3, h4, h5, h6, p, pre, ul, ol');
    
    elements.forEach((element, index) => {
      const anchor = this.createAnchorFromElement(element as HTMLElement, index);
      if (anchor) {
        this.anchors.push(anchor);
      }
    });

    // 按偏移量排序
    this.anchors.sort((a, b) => a.offset - b.offset);
    
    console.log(`SemanticScroll: Found ${this.anchors.length} anchors`);
  }

  private createAnchorFromElement(element: HTMLElement, index: number): SemanticAnchor | null {
    const tagName = element.tagName.toLowerCase();
    let type: SemanticAnchor['type'];
    let level: number | undefined;

    // 确定锚点类型
    if (tagName.startsWith('h')) {
      type = 'heading';
      level = parseInt(tagName.charAt(1));
    } else if (tagName === 'pre') {
      type = 'code';
    } else if (tagName === 'ul' || tagName === 'ol') {
      type = 'list';
    } else {
      type = 'paragraph';
    }

    // 获取文本内容
    const text = element.textContent?.trim() || '';
    if (!text) return null;

    // 生成稳定的ID
    const id = this.generateAnchorId(type, text, index);

    // 计算在文档中的偏移量
    const offset = this.calculateElementOffset(element);

    return {
      type,
      id,
      level,
      text: text.substring(0, 100), // 限制长度
      offset,
      element
    };
  }

  private generateAnchorId(type: string, text: string, index: number): string {
    // 使用类型、文本内容和索引生成稳定ID
    const cleanText = text.replace(/[^a-zA-Z0-9\u4e00-\u9fff]/g, '').substring(0, 20);
    return `${type}-${cleanText}-${index}`;
  }

  private calculateElementOffset(element: HTMLElement): number {
    // 基于段落ID计算偏移量（如果有的话）
    const segmentId = element.getAttribute('data-segment-id');
    if (segmentId) {
      const parts = segmentId.split(':');
      if (parts.length === 2) {
        const offset = parseInt(parts[1], 10);
        if (!isNaN(offset)) {
          return offset;
        }
      }
    }

    // 回退：基于DOM位置估算
    let offset = 0;
    let currentElement = this.container.firstElementChild;
    
    while (currentElement && currentElement !== element) {
      const text = currentElement.textContent || '';
      offset += text.length;
      currentElement = currentElement.nextElementSibling;
    }

    return offset;
  }

  /**
   * 捕获当前滚动位置的语义信息
   */
  captureCurrentPosition(): ScrollPosition | null {
    if (this.anchors.length === 0) {
      this.scanForAnchors();
    }

    const scrollTop = this.container.scrollTop;
    const containerRect = this.container.getBoundingClientRect();

    // 找到当前可见区域中最相关的锚点
    let bestAnchor: SemanticAnchor | null = null;
    let bestDistance = Infinity;

    for (const anchor of this.anchors) {
      if (!anchor.element) continue;

      const elementRect = anchor.element.getBoundingClientRect();
      const relativeTop = elementRect.top - containerRect.top;

      // 优先选择可见区域内的元素
      if (relativeTop >= 0 && relativeTop <= containerRect.height) {
        const distance = Math.abs(relativeTop - containerRect.height * 0.3); // 偏向上方30%位置
        if (distance < bestDistance) {
          bestDistance = distance;
          bestAnchor = anchor;
        }
      }
    }

    // 如果没有可见的锚点，选择最近的
    if (!bestAnchor && this.anchors.length > 0) {
      for (const anchor of this.anchors) {
        if (!anchor.element) continue;

        const elementRect = anchor.element.getBoundingClientRect();
        const distance = Math.abs(elementRect.top - containerRect.top);
        
        if (distance < bestDistance) {
          bestDistance = distance;
          bestAnchor = anchor;
        }
      }
    }

    if (bestAnchor && bestAnchor.element) {
      const elementRect = bestAnchor.element.getBoundingClientRect();
      const relativeOffset = Math.max(0, Math.min(1, 
        (containerRect.top - elementRect.top) / elementRect.height
      ));

      this.lastPosition = {
        anchor: bestAnchor,
        relativeOffset,
        pixelOffset: scrollTop
      };

      return this.lastPosition;
    }

    return null;
  }

  /**
   * 恢复到语义位置
   */
  restorePosition(position?: ScrollPosition): boolean {
    const targetPosition = position || this.lastPosition;
    if (!targetPosition) return false;

    this.isRestoring = true;

    try {
      // 重新扫描锚点（内容可能已变化）
      this.scanForAnchors();

      // 寻找匹配的锚点
      let targetAnchor = this.findMatchingAnchor(targetPosition.anchor);
      
      if (!targetAnchor || !targetAnchor.element) {
        // 找不到完全匹配，尝试找相似的
        targetAnchor = this.findSimilarAnchor(targetPosition.anchor);
      }

      if (targetAnchor && targetAnchor.element) {
        const elementRect = targetAnchor.element.getBoundingClientRect();
        const containerRect = this.container.getBoundingClientRect();

        // 计算目标滚动位置
        const targetScrollTop = this.container.scrollTop + 
          elementRect.top - containerRect.top +
          (elementRect.height * targetPosition.relativeOffset);

        // 平滑滚动到目标位置
        this.container.scrollTo({
          top: Math.max(0, targetScrollTop),
          behavior: 'smooth'
        });

        console.log(`SemanticScroll: Restored to anchor "${targetAnchor.id}"`);
        return true;
      }

      // 回退：使用像素偏移量
      this.container.scrollTo({
        top: Math.max(0, targetPosition.pixelOffset),
        behavior: 'smooth'
      });

      console.log('SemanticScroll: Fallback to pixel offset');
      return true;

    } finally {
      // 延迟重置标志，确保滚动完成
      setTimeout(() => {
        this.isRestoring = false;
      }, 300);
    }
  }

  private findMatchingAnchor(targetAnchor: SemanticAnchor): SemanticAnchor | null {
    return this.anchors.find(anchor => 
      anchor.id === targetAnchor.id ||
      (anchor.type === targetAnchor.type && 
       anchor.text === targetAnchor.text &&
       anchor.level === targetAnchor.level)
    ) || null;
  }

  private findSimilarAnchor(targetAnchor: SemanticAnchor): SemanticAnchor | null {
    // 按相似度排序
    const similarities = this.anchors.map(anchor => ({
      anchor,
      similarity: this.calculateSimilarity(targetAnchor, anchor)
    }));

    similarities.sort((a, b) => b.similarity - a.similarity);

    // 返回相似度最高且超过阈值的锚点
    if (similarities.length > 0 && similarities[0].similarity > 0.7) {
      return similarities[0].anchor;
    }

    return null;
  }

  private calculateSimilarity(a: SemanticAnchor, b: SemanticAnchor): number {
    let score = 0;

    // 类型匹配
    if (a.type === b.type) score += 0.3;

    // 级别匹配（标题）
    if (a.level && b.level && a.level === b.level) score += 0.2;

    // 文本相似度
    const textSimilarity = this.getTextSimilarity(a.text, b.text);
    score += textSimilarity * 0.5;

    return score;
  }

  private getTextSimilarity(text1: string, text2: string): number {
    if (text1 === text2) return 1;

    const longer = text1.length > text2.length ? text1 : text2;
    const shorter = text1.length > text2.length ? text2 : text1;

    if (longer.length === 0) return 1;

    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => 
      Array(str1.length + 1).fill(null)
    );

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * 获取当前位置信息
   */
  getCurrentPosition(): ScrollPosition | null {
    return this.lastPosition;
  }

  /**
   * 清除缓存的位置信息
   */
  clearPosition(): void {
    this.lastPosition = null;
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    // 移除事件监听器等清理工作
    this.anchors = [];
    this.lastPosition = null;
  }

  /**
   * 调试信息
   */
  debug(): void {
    console.log('SemanticScrollManager Debug Info:');
    console.log(`Anchors count: ${this.anchors.length}`);
    console.log('Current position:', this.lastPosition);
    console.log('Anchors:', this.anchors.map(a => ({
      id: a.id,
      type: a.type,
      text: a.text.substring(0, 30)
    })));
  }
} 