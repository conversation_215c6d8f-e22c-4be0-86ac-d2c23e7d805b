# 🔄 M2模式切换脚本

## 🎯 快速切换命令

### 方法一：修改EditorArea导入

```javascript
// 在控制台运行这个命令来查看当前使用的渲染器
function getCurrentRenderer() {
  console.log('🔍 检查当前渲染器...');
  
  // 检查简化模式
  if (typeof m2FallbackDebug !== 'undefined') {
    console.log('✅ 当前使用: 简化模式');
    console.log('📊 简化模式状态:', m2FallbackDebug.getStatus());
  }
  
  // 检查完整M2模式
  if (typeof m2Debug !== 'undefined') {
    console.log('✅ 当前使用: 完整M2模式');
    console.log('📊 M2模式状态:', m2Debug.getStatus());
  }
  
  // 检查状态指示器
  const blueIndicator = document.querySelector('.fixed.bottom-20.right-4');
  const orangeIndicator = document.querySelector('.fixed.bottom-20.right-16');
  
  if (blueIndicator) {
    console.log('🔵 M2系统指示器:', blueIndicator.textContent);
  }
  
  if (orangeIndicator) {
    console.log('🟠 简化模式指示器:', orangeIndicator.textContent);
  }
}

getCurrentRenderer();
```

### 方法二：性能对比

```javascript
// 对比两种模式的性能
function comparePerformance() {
  console.log('📊 性能对比分析...\n');
  
  // 检查DOM节点数
  const totalNodes = document.querySelectorAll('*').length;
  console.log(`🌐 DOM节点总数: ${totalNodes}`);
  
  // 检查渲染内容
  const proseElements = document.querySelectorAll('.prose');
  console.log(`📄 渲染区域数量: ${proseElements.length}`);
  
  proseElements.forEach((prose, index) => {
    const childNodes = prose.children.length;
    const textContent = prose.textContent?.length || 0;
    console.log(`  区域${index + 1}: ${childNodes}个元素, ${textContent}字符`);
  });
  
  // 内存使用情况
  if (performance.memory) {
    const memory = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
    console.log(`💾 内存使用: ${memory}MB`);
  }
  
  // 简单的渲染性能测试
  console.time('DOM查询性能');
  document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, pre, code');
  console.timeEnd('DOM查询性能');
}

comparePerformance();
```

## 🔧 切换步骤

### 切换到完整M2模式

如果简化模式工作正常，想要测试完整M2：

1. **修改EditorArea.tsx** - 将 `NativeDOMRendererM2Fallback` 改回 `NativeDOMRendererM2`
2. **刷新页面**
3. **运行调试脚本检查M2状态**

### 切换到简化模式

如果完整M2有问题，回到简化模式：

1. **修改EditorArea.tsx** - 将 `NativeDOMRendererM2` 改为 `NativeDOMRendererM2Fallback`
2. **刷新页面**
3. **验证内容显示正常**

## 📋 调试清单

### ✅ 简化模式检查项

- [ ] 右下角显示橙色 "简化模式" 指示器
- [ ] 预览模式能显示Markdown内容
- [ ] 分屏模式右侧能显示内容
- [ ] 控制台有 `m2FallbackDebug` 接口
- [ ] 内容编辑时有实时更新

### ✅ 完整M2模式检查项

- [ ] 右下角显示蓝色 "M2系统" 指示器
- [ ] 控制台显示M2初始化日志
- [ ] 控制台有 `m2Debug` 接口
- [ ] 性能指标显示正常
- [ ] 内容能正常渲染

## 🆘 故障排除

### 如果两种模式都不工作

```javascript
// 应急回退到原始渲染器
console.log('🆘 应急模式启动...');

// 检查是否有其他渲染器可用
console.log('可用渲染器:');
console.log('- NativeDOMRenderer (原始):', typeof NativeDOMRenderer);
console.log('- MarkdownRenderer:', typeof MarkdownRenderer);
console.log('- LiveRenderMode:', typeof LiveRenderMode);

// 如果需要，可以临时切换回原始渲染器
```

---

**当前状态: 已切换到简化模式，请验证内容是否正常显示！** 🔧 