import { generateStableSegmentId } from './hash-utils';

export interface TOCItem {
  id: string;
  level: number;
  title: string;
  anchor: string;
  offset: number;
  children: TOCItem[];
  parent?: TOCItem;
}

export interface TOCGenerationResult {
  toc: TOCItem[];
  headingMap: Map<string, TOCItem>;
  flatList: TOCItem[];
}

export class TOCGenerator {
  private headingRegex = /^(#{1,6})\s+(.+)$/gm;
  private lastTOC: TOCGenerationResult | null = null;

  /**
   * 从Markdown内容生成TOC结构
   */
  generateTOC(content: string): TOCGenerationResult {
    const tocItems: TOCItem[] = [];
    const headingMap = new Map<string, TOCItem>();
    const flatList: TOCItem[] = [];
    
    // 重置正则表达式索引
    this.headingRegex.lastIndex = 0;
    
    let match;
    let currentIndex = 0;
    
    while ((match = this.headingRegex.exec(content)) !== null) {
      const [fullMatch, hashes, title] = match;
      const level = hashes.length;
      const offset = match.index || 0;
      
      // 生成唯一ID
      const cleanTitle = this.cleanTitle(title);
      const id = this.generateHeadingId(cleanTitle, currentIndex);
      
      // 生成锚点
      const anchor = this.generateAnchor(cleanTitle, currentIndex);
      
      const tocItem: TOCItem = {
        id,
        level,
        title: title.trim(),
        anchor,
        offset,
        children: [],
      };
      
      tocItems.push(tocItem);
      headingMap.set(id, tocItem);
      flatList.push(tocItem);
      
      currentIndex++;
    }
    
    // 构建层级结构
    const hierarchicalTOC = this.buildHierarchy(tocItems);
    
    const result: TOCGenerationResult = {
      toc: hierarchicalTOC,
      headingMap,
      flatList
    };
    
    this.lastTOC = result;
    return result;
  }

  /**
   * 构建层级结构
   */
  private buildHierarchy(flatItems: TOCItem[]): TOCItem[] {
    const root: TOCItem[] = [];
    const stack: TOCItem[] = [];

    for (const item of flatItems) {
      // 找到合适的父级
      while (stack.length > 0 && stack[stack.length - 1].level >= item.level) {
        stack.pop();
      }

      if (stack.length === 0) {
        // 顶级项目
        root.push(item);
      } else {
        // 添加为子项目
        const parent = stack[stack.length - 1];
        parent.children.push(item);
        item.parent = parent;
      }

      stack.push(item);
    }

    return root;
  }

  /**
   * 清理标题文本
   */
  private cleanTitle(title: string): string {
    return title
      .trim()
      .replace(/[^\w\u4e00-\u9fff\s-]/g, '') // 保留字母、数字、中文、空格、连字符
      .replace(/\s+/g, '-') // 空格转连字符
      .toLowerCase();
  }

  /**
   * 生成标题ID
   */
  private generateHeadingId(cleanTitle: string, index: number): string {
    return `heading-${cleanTitle}-${index}`;
  }

  /**
   * 生成锚点
   */
  private generateAnchor(cleanTitle: string, index: number): string {
    if (cleanTitle.length > 0) {
      return `#${cleanTitle}`;
    }
    return `#heading-${index}`;
  }

  /**
   * 获取指定级别的TOC
   */
  getTOCByLevel(maxLevel: number, result?: TOCGenerationResult): TOCItem[] {
    const source = result || this.lastTOC;
    if (!source) return [];

    return this.filterByLevel(source.toc, maxLevel);
  }

  private filterByLevel(items: TOCItem[], maxLevel: number): TOCItem[] {
    const filtered: TOCItem[] = [];

    for (const item of items) {
      if (item.level <= maxLevel) {
        const newItem: TOCItem = {
          ...item,
          children: this.filterByLevel(item.children, maxLevel)
        };
        filtered.push(newItem);
      }
    }

    return filtered;
  }

  /**
   * 查找最近的标题
   */
  findNearestHeading(offset: number, result?: TOCGenerationResult): TOCItem | null {
    const source = result || this.lastTOC;
    if (!source) return null;

    let nearest: TOCItem | null = null;
    let minDistance = Infinity;

    for (const item of source.flatList) {
      const distance = Math.abs(item.offset - offset);
      if (distance < minDistance && item.offset <= offset) {
        minDistance = distance;
        nearest = item;
      }
    }

    return nearest;
  }

  /**
   * 获取标题的完整路径
   */
  getHeadingPath(item: TOCItem): string[] {
    const path: string[] = [];
    let current: TOCItem | undefined = item;

    while (current) {
      path.unshift(current.title);
      current = current.parent;
    }

    return path;
  }

  /**
   * 生成导航面包屑
   */
  generateBreadcrumb(offset: number, result?: TOCGenerationResult): string[] {
    const nearest = this.findNearestHeading(offset, result);
    if (!nearest) return [];

    return this.getHeadingPath(nearest);
  }

  /**
   * 获取平铺的标题列表（用于搜索）
   */
  getFlatHeadings(result?: TOCGenerationResult): Array<{
    id: string;
    title: string;
    level: number;
    path: string[];
    offset: number;
  }> {
    const source = result || this.lastTOC;
    if (!source) return [];

    return source.flatList.map(item => ({
      id: item.id,
      title: item.title,
      level: item.level,
      path: this.getHeadingPath(item),
      offset: item.offset
    }));
  }

  /**
   * 搜索标题
   */
  searchHeadings(query: string, result?: TOCGenerationResult): TOCItem[] {
    const source = result || this.lastTOC;
    if (!source) return [];

    const lowerQuery = query.toLowerCase();
    
    return source.flatList.filter(item =>
      item.title.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * 生成Markdown格式的TOC
   */
  generateMarkdownTOC(maxLevel: number = 6, result?: TOCGenerationResult): string {
    const source = result || this.lastTOC;
    if (!source) return '';

    const lines: string[] = [];
    
    const processItems = (items: TOCItem[], depth: number = 0) => {
      for (const item of items) {
        if (item.level <= maxLevel) {
          const indent = '  '.repeat(depth);
          const link = `[${item.title}](${item.anchor})`;
          lines.push(`${indent}- ${link}`);
          
          if (item.children.length > 0) {
            processItems(item.children, depth + 1);
          }
        }
      }
    };

    processItems(source.toc);
    return lines.join('\n');
  }

  /**
   * 生成HTML格式的TOC
   */
  generateHTMLTOC(maxLevel: number = 6, result?: TOCGenerationResult): string {
    const source = result || this.lastTOC;
    if (!source) return '';

    const processItems = (items: TOCItem[]): string => {
      if (items.length === 0) return '';

      const listItems = items
        .filter(item => item.level <= maxLevel)
        .map(item => {
          const childHTML = item.children.length > 0 
            ? processItems(item.children) 
            : '';
          
          return `<li>
            <a href="${item.anchor}" data-offset="${item.offset}">${item.title}</a>
            ${childHTML}
          </li>`;
        })
        .join('\n');

      return `<ul>\n${listItems}\n</ul>`;
    };

    return processItems(source.toc);
  }

  /**
   * 增量更新TOC（基于内容变化）
   */
  updateTOC(newContent: string, changedRange?: { start: number; end: number }): TOCGenerationResult {
    // M2阶段简化实现：完全重新生成
    // 后续可以基于changedRange实现真正的增量更新
    return this.generateTOC(newContent);
  }

  /**
   * 获取统计信息
   */
  getTOCStats(result?: TOCGenerationResult): {
    totalHeadings: number;
    byLevel: Record<number, number>;
    maxDepth: number;
    avgDepth: number;
  } {
    const source = result || this.lastTOC;
    if (!source) {
      return {
        totalHeadings: 0,
        byLevel: {},
        maxDepth: 0,
        avgDepth: 0
      };
    }

    const byLevel: Record<number, number> = {};
    let maxDepth = 0;
    let totalDepth = 0;

    for (const item of source.flatList) {
      byLevel[item.level] = (byLevel[item.level] || 0) + 1;
      maxDepth = Math.max(maxDepth, item.level);
      totalDepth += item.level;
    }

    return {
      totalHeadings: source.flatList.length,
      byLevel,
      maxDepth,
      avgDepth: source.flatList.length > 0 ? totalDepth / source.flatList.length : 0
    };
  }

  /**
   * 验证TOC完整性
   */
  validateTOC(result?: TOCGenerationResult): {
    isValid: boolean;
    errors: string[];
  } {
    const source = result || this.lastTOC;
    if (!source) {
      return { isValid: false, errors: ['No TOC data available'] };
    }

    const errors: string[] = [];

    // 检查ID唯一性
    const ids = new Set<string>();
    for (const item of source.flatList) {
      if (ids.has(item.id)) {
        errors.push(`Duplicate heading ID: ${item.id}`);
      }
      ids.add(item.id);
    }

    // 检查层级逻辑
    for (let i = 1; i < source.flatList.length; i++) {
      const prev = source.flatList[i - 1];
      const curr = source.flatList[i];
      
      if (curr.level > prev.level + 1) {
        errors.push(`Heading level jump from ${prev.level} to ${curr.level} at "${curr.title}"`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取最近生成的TOC
   */
  getLastTOC(): TOCGenerationResult | null {
    return this.lastTOC;
  }

  /**
   * 清除缓存的TOC
   */
  clearCache(): void {
    this.lastTOC = null;
  }

  /**
   * 调试信息
   */
  debug(result?: TOCGenerationResult): void {
    const source = result || this.lastTOC;
    if (!source) {
      console.log('TOCGenerator: No TOC data available');
      return;
    }

    console.log('TOCGenerator Debug Info:');
    console.log('Stats:', this.getTOCStats(source));
    console.log('Validation:', this.validateTOC(source));
    console.log('Flat list:', source.flatList.map(item => ({
      level: item.level,
      title: item.title,
      offset: item.offset
    })));
  }
} 