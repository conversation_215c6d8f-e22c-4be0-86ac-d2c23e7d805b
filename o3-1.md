# ✅ 专家复核 —— “aug 1 分析报告” + “虚拟滚动最终方案” 对比结论

> **总体评价**：两份文档与此前专家路线 **基本一致**，可以作为最终实装蓝本。  
> 仅有 **3 个可调参数** 与 **2 处实现细节** 需对齐，避免后期闪白 / 掉帧 / 滚动丢块。

---

## 1. 参数差异 & 调整建议

| 参数 | 方案文档值 | 专家建议值 | 调整理由 |
|------|-----------|------------|----------|
| `overscan` | **2**【:contentReference[oaicite:4]{index=4}】 | **8**（±2 屏） | overscan 2 会在 **高速滚轮** 或 **触控板惯性** 下出现空白；8 在 400 px 视口内额外渲染 ≤ 16 个块，DOM 仍 < 50，安全系数更高。|
| `estimateSize` | **固定 40**【:contentReference[oaicite:5]{index=5}】 | **动态平均**：`avgHeightRef` 运行更新 | 同一段落有图片/代码块时高度差异大；动态平均可在首屏后快速逼近真实高度，减少回弹。|
| 容器高度示例 | `height: '400px'` 固定【:contentReference[oaicite:6]{index=6}】 | **生产**应改为 `className="flex-1 min-h-0"` | 示例 400 px 只作 demo；正式页面应填充父级高度，否则分屏/全屏场景滚动区域不足。|

> **操作**：  
> 1. 把 `overscan: 2` 改为 `overscan: 8`；  
> 2. 将 `estimateSize` 改成  
>    ```ts
>    const estimateSize = useCallback(()=>avgHeightRef.current ?? 40,[]);
>    ```  
>    并在 `measureElement` 回调内更新 `avgHeightRef`；  
> 3. 布局容器使用 `flex-1 overflow-auto min-h-0` 替代固定 400 px。

---

## 2. 实现细节需补齐

| 细节 | 当前状态 | 必须补充 |
|------|----------|----------|
| **块高度测量** (`measureElement`) | 文档示例已挂 ref【L64-L76】 | 确保 ParagraphBlock 用 `forwardRef<HTMLDivElement>`，实际渲染节点必须挂上，否则 estimateSize 永远 40。 |
| **Worker 批量超时策略** | 文档未提及 | 继续沿用专家 2 s timeout + batch≤200 + slow batch 拆半重投（指数退避），避免慢段拖垮 UI。 |

---

## 3. 与专家路线 **完全一致** 的部分（可直接采用）

1. **LongTasks + 输入延迟监控** —— Phase 1 就接入 ✔  
2. **DOM、FPS、P95 指标门槛** —— DOM≤30/输入<150 ms/FPS≥55 ✔  
3. **懒渲染（图片/代码块）+ 动态高度二次测量** ✔  
4. **WorkerPool rev 号、DataCloneError 处理、超时 fallback** ✔  

---

## 4. 建议的最终落地顺序

1. **调参**：overscan 8 / 动态 estimateSize / flex 容器。  
2. **确认高度测量**：滚动一次，观察 `avgHeightRef` 是否 > 40 并平稳。  
3. **重跑 1 500 段长文**：  
   * DOM 节点 ≤ 35  
   * FPS 采样 ≥ 55  
   * 输入→paint P95 < 150 ms  
4. **Playwright & Lighthouse**：全部指标绿灯后合并到 `main`。  

---

## 5. 如仍卡顿 / 闪白，请回帖

- **DevTools Performance** 30 fps 以下片段 flamegraph  
- **Worker fallback 日志**（带 rev 和 reason）  
- `rowVirtualizer.getVirtualItems().length` 与 `blocks.length` 对比值  

我会在 **12 h 内** 给出补丁或参数修正。

---

### ✅ 若按以上微调执行，方案与专家讨论 **不存在冲突**，可直接排期上线。
