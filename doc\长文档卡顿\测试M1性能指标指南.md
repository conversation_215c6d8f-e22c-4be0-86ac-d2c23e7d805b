# M1阶段性能验收测试指南

## 🎯 验收目标

M1阶段必须达成的硬性指标：
- ✅ **帧率 (FPS)**: ≥50fps
- ✅ **输入延迟**: P95 <100ms
- ✅ **长任务**: <80ms
- ✅ **DOM节点**: ≤2500
- ✅ **功能完整性**: 所有现有功能正常运行

## 🚀 快速验收方法

### 方法一：自动化测试面板

1. **启动开发服务器**：
   ```bash
   npm run dev
   ```

2. **打开应用**：访问 `http://localhost:3000`

3. **打开测试面板**：
   - 按 `Ctrl+Shift+T` (Windows/Linux)
   - 按 `Cmd+Shift+T` (Mac)
   - 或点击左下角的 "M1测试" 按钮

4. **执行测试**：
   - 点击 "开始测试" 按钮
   - 等待测试完成（约1-2秒）
   - 查看结果：绿色✅表示通过，红色❌表示未通过

5. **查看详细报告**：
   - 点击 "下载报告" 获取详细测试报告
   - 或在控制台查看自动输出的报告

### 方法二：手动控制台验收

1. **打开浏览器控制台** (F12)

2. **运行性能测试脚本**：
   ```javascript
   // 获取当前指标
   performanceMonitor.getMetrics()
   
   // 生成详细报告
   console.log(performanceMonitor.generateReport())
   
   // 运行完整验收测试
   function runM1Acceptance() {
     const metrics = performanceMonitor.getMetrics();
     console.log('🧪 M1阶段验收测试开始...\n');
     
     const results = {
       fps: { pass: metrics.fps >= 50, value: metrics.fps, target: '≥50' },
       latency: { pass: metrics.inputLatency.p95 < 100, value: metrics.inputLatency.p95, target: '<100ms' },
       longTask: { pass: metrics.longTasks.maxDuration < 80, value: metrics.longTasks.maxDuration, target: '<80ms' },
       domNodes: { pass: metrics.domNodeCount <= 2500, value: metrics.domNodeCount, target: '≤2500' }
     };
     
     Object.entries(results).forEach(([key, result]) => {
       const status = result.pass ? '✅' : '❌';
       console.log(`${status} ${key}: ${result.value} (目标: ${result.target})`);
     });
     
     const allPassed = Object.values(results).every(r => r.pass);
     console.log(`\n📊 总体结果: ${allPassed ? '✅ 验收通过' : '❌ 验收失败'}`);
     
     return allPassed;
   }
   
   // 运行测试
   runM1Acceptance();
   ```

### 方法三：Chrome DevTools验收

1. **打开Chrome DevTools** (F12)

2. **切换到Performance面板**

3. **开始录制**：
   - 点击录制按钮 (圆形按钮)
   - 在编辑器中连续输入500+字符
   - 停止录制 (约10-15秒)

4. **查看关键指标**：
   - **FPS**: 顶部的绿色条形图，应保持在50fps以上
   - **Main Thread**: 红色块表示长任务，应该<80ms
   - **Long Tasks**: 查看是否有超过50ms的任务

## 🔧 实际功能验收

### 输入响应测试

1. **快速连续输入**：
   - 在编辑器中快速输入大段文字
   - 观察预览区更新是否流畅
   - **预期结果**: 输入响应流畅，无明显卡顿

2. **复制粘贴测试**：
   - 粘贴大段Markdown内容（1000+行）
   - 观察处理速度和响应性
   - **预期结果**: 处理迅速，界面不冻结

### 滚动性能测试

1. **创建长文档**：
   ```markdown
   # 测试文档
   
   ## 段落测试
   这是一个测试段落...
   
   ## 代码块测试
   ```javascript
   function test() {
     console.log('测试');
   }
   ```
   
   // 复制上述内容200次创建长文档
   ```

2. **滚动测试**：
   - 快速滚动编辑器和预览区
   - 使用鼠标滚轮和拖拽滚动条
   - **预期结果**: 滚动流畅，≥50fps

### DOM节点监控

```javascript
// 检查DOM节点数量
console.log('DOM节点总数:', document.querySelectorAll('*').length);

// 检查段落ID稳定性
Array.from(document.querySelectorAll('[data-segment-id]'))
  .map(el => el.getAttribute('data-segment-id'))
  .forEach(id => console.log('段落ID:', id));
```

## 📊 性能基准对比

### M1优化前 vs 优化后

| 指标 | 优化前 | M1目标 | 验收标准 |
|------|---------|---------|----------|
| 输入延迟 | 50-200ms | <100ms | ✅ P95 <100ms |
| 滚动FPS | 30-45fps | ≥50fps | ✅ ≥50fps |
| 长任务 | 100-300ms | <80ms | ✅ <80ms |
| DOM重建 | 每次全量 | 增量更新 | ✅ 稳定段落ID |

### 核心改进验证

1. **稳定段落ID**：
   ```javascript
   // 插入内容前
   const beforeIds = Array.from(document.querySelectorAll('[data-segment-id]'))
     .map(el => el.getAttribute('data-segment-id'));
   
   // 在文档开头插入内容
   // 插入内容后
   const afterIds = Array.from(document.querySelectorAll('[data-segment-id]'))
     .map(el => el.getAttribute('data-segment-id'));
   
   // 验证：除了新增的段落，其他段落ID应该保持不变
   console.log('ID稳定性验证:', beforeIds.filter(id => afterIds.includes(id)).length);
   ```

2. **防抖机制验证**：
   - 快速连续输入字符
   - 观察控制台的渲染性能日志
   - **预期**: 120ms防抖生效，减少无效渲染

3. **keyed-diff验证**：
   - 编辑中间段落
   - 检查是否只更新变化的段落
   - **预期**: 不再出现innerHTML清空操作

## 🐛 常见问题排查

### 问题1：性能面板不显示

**检查**：
```javascript
// 确认性能监控已启用
performanceMonitor.getMetrics()

// 检查开发环境
console.log('Environment:', process.env.NODE_ENV);

// 检查浏览器支持
if ('PerformanceObserver' in window) {
  console.log('✅ 支持性能监控');
} else {
  console.log('❌ 浏览器不支持');
}
```

### 问题2：指标不达标

**排查步骤**：
1. 检查是否安装了所有依赖
2. 确认没有其他应用占用CPU
3. 测试不同大小的文档
4. 清除浏览器缓存重新测试

### 问题3：功能回归

**验证清单**：
- [ ] 编辑器正常输入
- [ ] 预览实时更新
- [ ] 滚动位置同步
- [ ] 大纲跳转功能
- [ ] 主题切换正常
- [ ] 文件操作正常

## 📈 验收通过标准

### 必须达标项 ✅

- **性能指标**: 所有4项核心指标达标
- **功能完整**: 所有现有功能正常
- **稳定性**: 长时间使用无明显性能衰减
- **兼容性**: 主流浏览器正常运行

### 验收通过确认

当您看到以下结果时，表示M1阶段验收通过：

```
🎉 M1阶段性能优化验收通过！

✅ 帧率: 55.0fps (目标: ≥50fps)
✅ 输入延迟: 85.2ms (目标: <100ms)  
✅ 长任务: 45.1ms (目标: <80ms)
✅ DOM节点: 1250 (目标: ≤2500)

准备进入M2阶段：局部重分段 + 语义锚点滚动
```

## 🚀 验收通过后的下一步

M1验收通过后，我们将进入M2阶段，包括：

1. **局部重分段** - 基于CodeMirror changes的精确增量解析
2. **语义锚点滚动** - heading + 段内偏移的精确定位  
3. **自适应防抖** - 根据文档大小动态调整防抖时间
4. **TOC一次产出** - 解析时直接生成目录，避免重复计算

---

**开始验收测试吧！按 Ctrl+Shift+T 打开测试面板或使用控制台命令开始验证M1的性能改进效果！** 🎯 