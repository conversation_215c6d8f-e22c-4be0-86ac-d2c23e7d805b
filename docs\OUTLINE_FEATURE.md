# 大纲功能使用指南

## 功能概述

大纲功能自动解析当前Markdown文档中的标题（H1-H6），生成可交互的文档目录树，帮助用户快速导航和理解文档结构。

## 主要特性

### 📋 自动标题提取
- 自动解析Markdown文档中的所有标题（# ## ### #### ##### ######）
- 实时更新大纲内容，跟随文档编辑变化
- 支持中文、英文标题的智能ID生成

### 🌳 分层级展示
- 清晰的层级结构展示
- 可展开/折叠不同级别的标题
- 默认展开H1和H2级别标题
- 视觉层级指示（字体粗细、缩进）

### 🎯 精确跳转
- 点击大纲项快速跳转到对应内容
- 支持编辑模式和预览模式的跳转
- 平滑滚动动画，优秀的用户体验

### 🎨 美观界面
- 悬停效果和激活状态高亮
- 自适应的字体大小和权重
- 行号显示（悬停时可见）
- 统计信息显示

## 使用方法

### 基本操作

1. **查看大纲**
   - 在左侧边栏点击"大纲"图标（📄 列表图标）
   - 自动显示当前文档的标题结构

2. **跳转到标题**
   - 点击任意大纲项即可跳转到对应位置
   - 编辑器会自动滚动并定位到该行

3. **展开/折叠**
   - 点击标题前的箭头图标展开或折叠子级别
   - 使用"全部展开"和"折叠"按钮快速控制

### 快捷操作

- **全部展开**: 展开所有层级的标题
- **折叠**: 只显示顶级标题（H1）
- **行号提示**: 悬停在标题上显示对应的行号

## 技术实现

### 标题解析算法
```typescript
// 解析Markdown内容，提取标题
const parseHeadings = (markdown: string): HeadingItem[] => {
  const lines = markdown.split('\n');
  const headings: HeadingItem[] = [];
  
  lines.forEach((line, index) => {
    // 匹配 ATX 标题格式 (# ## ### 等)
    const atxMatch = line.match(/^(#{1,6})\s+(.+)$/);
    if (atxMatch) {
      const level = atxMatch[1].length;
      const text = atxMatch[2].trim();
      const id = generateHeadingId(text);
      
      headings.push({
        id: id || `heading-${index}`,
        text,
        level,
        line: index + 1
      });
    }
  });

  return headings;
};
```

### ID生成规则
- 转换为小写
- 保留中文、英文、数字、空格、连字符
- 空格替换为连字符
- 移除开头和结尾的连字符

### 跳转实现
- **编辑模式**: 使用CodeMirror API精确定位到指定行
- **预览模式**: 使用DOM滚动到对应的标题元素
- **事件系统**: 通过自定义事件在组件间通信

## 最佳实践

### 标题写作建议
1. 使用清晰、描述性的标题
2. 保持合理的标题层级结构
3. 避免过深的嵌套（建议不超过4级）
4. 标题应该概括章节内容

### 使用技巧
1. 经常使用大纲功能来检查文档结构
2. 利用折叠功能聚焦特定章节
3. 在长文档中使用大纲快速定位内容
4. 结合快捷键提高编辑效率

## 注意事项

- 大纲功能依赖标准的Markdown标题语法
- 需要选择一个文件才能显示大纲
- 空文档或无标题文档会显示相应提示
- 跳转功能在不同编辑模式下行为略有差异

## 后续优化计划

- [ ] 添加标题搜索功能
- [ ] 支持标题编辑和重命名
- [ ] 添加标题级别调整工具
- [ ] 实现大纲导出功能
- [ ] 支持自定义大纲样式

---

大纲功能让您的Markdown编辑体验更加高效和直观！🚀 