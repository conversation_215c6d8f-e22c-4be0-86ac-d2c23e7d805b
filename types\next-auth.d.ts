import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface Session {
    accessToken?: string;
    refreshToken?: string;
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      githubId?: string;
      githubLogin?: string;
      githubName?: string;
      githubEmail?: string;
      githubAvatar?: string;
    };
  }

  interface User {
    githubId?: string;
    githubLogin?: string;
    githubName?: string;
    githubEmail?: string;
    githubAvatar?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    accessToken?: string;
    refreshToken?: string;
    githubId?: string;
    githubLogin?: string;
    githubName?: string;
    githubEmail?: string;
    githubAvatar?: string;
  }
}
