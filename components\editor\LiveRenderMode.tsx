'use client';

import React, { useState, useCallback, useMemo, useRef } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';

interface LiveRenderModeProps {
  content: string;
  onChange: (content: string) => void;
  className?: string;
}

interface ParagraphData {
  id: string;
  markdown: string;
  html: string;
  isEditing: boolean;
}

// 渲染Markdown为HTML的函数
const renderMarkdown = (markdown: string) => {
  try {
    const processor = unified()
      .use(remarkParse)
      .use(remarkGfm)
      .use(remarkRehype, { allowDangerousHtml: true })
      .use(rehypeRaw)
      .use(rehypeHighlight)
      .use(rehypeStringify);

    const result = processor.processSync(markdown);
    return String(result);
  } catch (error) {
    console.error('Markdown rendering error:', error);
    return '<p>渲染错误</p>';
  }
};

export default function LiveRenderMode({
  content,
  onChange,
  className = ''
}: LiveRenderModeProps) {
  const [editingParagraphId, setEditingParagraphId] = useState<string | null>(null);

  // 虚拟滚动容器引用
  const parentRef = useRef<HTMLDivElement>(null);

  // 动态高度估算
  const avgHeightRef = useRef(40);

  // 专家建议的性能监控 - LongTasks API
  const longTaskObserver = React.useMemo(() => {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          console.warn('🚨 Long task detected:', {
            duration: entry.duration,
            startTime: entry.startTime,
            name: entry.name
          });
        });
      });
      observer.observe({ entryTypes: ['longtask'] });
      return observer;
    }
    return null;
  }, []);

  // 输入响应时间监控
  const measureInputResponse = React.useCallback(() => {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      console.log('⚡ 输入响应时间:', duration, 'ms');

      if (duration > 150) {
        console.warn('🚨 输入响应超时:', duration, 'ms');
      }
    };
  }, []);

  // 监听大纲跳转事件
  React.useEffect(() => {
    const handleScrollToLiveHeading = (event: CustomEvent<{ headingId: string; line: number; headingText: string }>) => {
      const { headingId, line } = event.detail;
      console.log(`LiveRenderMode: 跳转到标题 ${headingId}, 行 ${line}`);
      
      // 尝试通过ID查找对应的元素
      const targetElement = document.querySelector(`#${headingId}`);
      if (targetElement) {
        targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        return;
      }
      
      // 如果找不到ID，尝试通过段落内容查找
      // 计算目标行在哪个段落中
      const lines = content.split('\n');
      let currentLine = 1;
      let targetParagraphIndex = -1;
      
      const contentLines = content.split('\n');
      for (let i = 0; i < contentLines.length; i++) {
        if (i + 1 === line) {
          // 找到对应行，确定属于哪个段落
          let paragraphStart = i;
          // 向上找到段落开始
          while (paragraphStart > 0 && contentLines[paragraphStart - 1].trim() !== '') {
            paragraphStart--;
          }
          
          // 计算这是第几个段落
          let paragraphCount = 0;
          for (let j = 0; j < paragraphStart; j++) {
            if (contentLines[j].trim() === '' && j > 0 && contentLines[j - 1].trim() !== '') {
              paragraphCount++;
            }
          }
          if (paragraphStart === 0 || contentLines[paragraphStart].trim() !== '') {
            targetParagraphIndex = paragraphCount;
          }
          break;
        }
      }
      
      // 滚动到对应段落
      if (targetParagraphIndex >= 0) {
        const paragraphElement = document.querySelector(`[data-paragraph-index="${targetParagraphIndex}"]`);
        if (paragraphElement) {
          paragraphElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    };

    document.addEventListener('scrollToLiveHeading', handleScrollToLiveHeading as EventListener);
    
    return () => {
      document.removeEventListener('scrollToLiveHeading', handleScrollToLiveHeading as EventListener);
    };
  }, [content]);

  // 将内容分割成段落 (保持现有逻辑)
  const blocks = useMemo(() => {
    const lines = content.split('\n');
    const blocks: ParagraphData[] = [];
    let currentParagraph = '';
    let paragraphIndex = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 如果是空行，结束当前段落
      if (line.trim() === '') {
        if (currentParagraph.trim() !== '') {
          blocks.push({
            id: `paragraph-${paragraphIndex}`,
            markdown: currentParagraph.trim(),
            html: renderMarkdown(currentParagraph.trim()),
            isEditing: editingParagraphId === `paragraph-${paragraphIndex}`
          });
          paragraphIndex++;
        }
        currentParagraph = '';
      } else {
        currentParagraph += (currentParagraph ? '\n' : '') + line;
      }
    }

    // 处理最后一个段落
    if (currentParagraph.trim() !== '') {
      blocks.push({
        id: `paragraph-${paragraphIndex}`,
        markdown: currentParagraph.trim(),
        html: renderMarkdown(currentParagraph.trim()),
        isEditing: editingParagraphId === `paragraph-${paragraphIndex}`
      });
    }

    console.log('📊 段落统计:', {
      总段落数: blocks.length,
      虚拟滚动触发: blocks.length > 50 ? '✅ 启用' : '❌ 未启用'
    });

    return blocks;
  }, [content, editingParagraphId]);

  // 专家建议的虚拟滚动配置
  const estimateSize = useCallback(() => avgHeightRef.current ?? 40, []);

  const rowVirtualizer = useVirtualizer({
    count: blocks.length,
    getScrollElement: () => parentRef.current,
    estimateSize: estimateSize,  // 专家建议：动态平均高度
    overscan: 8,                 // 专家建议：从2改为8，避免高速滚动空白
  });

  // 高度测量回调
  const measureElement = useCallback((element: Element | null) => {
    if (element) {
      const height = element.getBoundingClientRect().height;
      // 运行平均值更新 (专家建议的动态估算)
      avgHeightRef.current = (avgHeightRef.current * 0.9) + (height * 0.1);
      return height;
    }
    return avgHeightRef.current;
  }, []);

  // 开始编辑段落
  const startEditing = useCallback((paragraphId: string) => {
    const endMeasure = measureInputResponse();
    setEditingParagraphId(paragraphId);
    endMeasure(); // 测量输入响应时间
  }, [measureInputResponse]);

  // 完成编辑段落
  const finishEditing = useCallback((paragraphId: string, newMarkdown: string) => {
    const endMeasure = measureInputResponse();
    const paragraphIndex = parseInt(paragraphId.split('-')[1]);
    const newBlocks = [...blocks];

    // 更新段落内容
    if (newBlocks[paragraphIndex]) {
      newBlocks[paragraphIndex].markdown = newMarkdown;
    }

    // 重新构建完整内容
    const newContent = newBlocks.map(p => p.markdown).join('\n\n');
    onChange(newContent);
    setEditingParagraphId(null);
    endMeasure(); // 测量编辑完成响应时间
  }, [blocks, onChange, measureInputResponse]);

  // 取消编辑
  const cancelEditing = useCallback(() => {
    setEditingParagraphId(null);
  }, []);

  // 专家建议的虚拟滚动渲染
  return (
    <div
      ref={parentRef}
      id="virtual-parent"
      className={`flex-1 overflow-auto min-h-0 ${className}`}
      style={{
        position: 'relative',
        // 专家建议：生产环境使用flex-1而不是固定400px
        height: '100%'
      }}
    >
      <div
        style={{
          height: rowVirtualizer.getTotalSize(),
          position: 'relative',
          width: '100%',
        }}
      >
        {/* 🔥 核心：只渲染可见的虚拟项目 */}
        {rowVirtualizer.getVirtualItems().map((virtualItem) => {
          const block = blocks[virtualItem.index];
          if (!block) return null;

          return (
            <div
              key={virtualItem.key}
              ref={rowVirtualizer.measureElement}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                transform: `translateY(${virtualItem.start}px)`,
              }}
            >
              <div className="p-2">
                <ParagraphBlock
                  paragraph={block}
                  paragraphIndex={virtualItem.index}
                  onStartEdit={() => startEditing(block.id)}
                  onFinishEdit={(newMarkdown) => finishEditing(block.id, newMarkdown)}
                  onCancelEdit={cancelEditing}
                  onMeasure={measureElement}
                />
              </div>
            </div>
          );
        })}
      </div>

      {blocks.length === 0 && (
        <div className="text-muted-foreground text-center py-8">
          开始编写您的 Markdown 内容...
        </div>
      )}

      {/* 性能调试信息 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 right-4 bg-black/80 text-white text-xs p-2 rounded">
          📊 总段落: {blocks.length} |
          可见项: {rowVirtualizer.getVirtualItems().length} |
          虚拟滚动: {blocks.length > 10 ? '✅' : '❌'}
        </div>
      )}
    </div>
  );
}

interface ParagraphBlockProps {
  paragraph: ParagraphData;
  paragraphIndex: number;
  onStartEdit: () => void;
  onFinishEdit: (newMarkdown: string) => void;
  onCancelEdit: () => void;
  onMeasure?: (element: Element | null) => void;
}

// 专家建议：使用 forwardRef 确保高度测量正确
const ParagraphBlock = React.forwardRef<HTMLDivElement, ParagraphBlockProps>(({
  paragraph,
  paragraphIndex,
  onStartEdit,
  onFinishEdit,
  onCancelEdit,
  onMeasure
}, ref) => {
  const [editingContent, setEditingContent] = useState(paragraph.markdown);

  // 测量元素高度
  const measureRef = useCallback((element: HTMLDivElement | null) => {
    if (element && onMeasure) {
      onMeasure(element);
    }
    if (typeof ref === 'function') {
      ref(element);
    } else if (ref) {
      ref.current = element;
    }
  }, [onMeasure, ref]);

  // 为段落中的标题添加ID
  const processedHtml = React.useMemo(() => {
    let html = paragraph.html;
    // 为标题添加ID，与MarkdownRenderer保持一致
    html = html.replace(
      /<(h[1-6])>([^<]+)<\/h[1-6]>/g,
      (match, tag, text) => {
        const id = text.toLowerCase()
          .replace(/[^\w\u4e00-\u9fa5\s-]/g, '') // 保留中文、英文、数字、空格、连字符
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '');
        
        if (id) {
          return `<${tag} id="${id}">${text}</${tag}>`;
        }
        return match;
      }
    );
    return html;
  }, [paragraph.html]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      onFinishEdit(editingContent);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      setEditingContent(paragraph.markdown);
      onCancelEdit();
    }
  };

  if (paragraph.isEditing) {
    return (
      <div
        ref={measureRef}
        className="border border-primary rounded-lg p-3 bg-muted/50"
        data-paragraph-index={paragraphIndex}
      >
        <textarea
          value={editingContent}
          onChange={(e) => setEditingContent(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={() => onFinishEdit(editingContent)}
          className="w-full h-24 resize-none border-none outline-none bg-transparent font-mono text-sm"
          placeholder="编辑段落内容... (Ctrl+Enter保存, Esc取消)"
          autoFocus
        />
        <div className="text-xs text-muted-foreground mt-2">
          Ctrl+Enter 保存 • Esc 取消
        </div>
      </div>
    );
  }

  return (
    <div
      ref={measureRef}
      className="prose prose-sm max-w-none dark:prose-invert cursor-pointer hover:bg-muted/30 rounded-lg p-3 transition-colors"
      onClick={onStartEdit}
      data-paragraph-index={paragraphIndex}
      dangerouslySetInnerHTML={{ __html: processedHtml }}
    />
  );
});

// 设置显示名称以便调试
ParagraphBlock.displayName = 'ParagraphBlock';

export { ParagraphBlock };
