/**
 * 安全的Markdown渲染器 - M1阶段安全基线实现
 * 包含内容消毒、外链安全处理和图片懒加载
 */

import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';

// 安全渲染配置
const SECURITY_CONFIG = {
  // 允许的HTML标签
  allowedTags: [
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'p', 'div', 'span', 'br',
    'strong', 'em', 'b', 'i', 'u', 's',
    'ul', 'ol', 'li',
    'blockquote', 'pre', 'code',
    'a', 'img',
    'table', 'thead', 'tbody', 'tr', 'th', 'td',
    'hr', 'del', 'ins'
  ],
  
  // 允许的属性
  allowedAttributes: {
    'a': ['href', 'title', 'rel', 'target'],
    'img': ['src', 'alt', 'title', 'width', 'height', 'loading'],
    'h1': ['id'], 'h2': ['id'], 'h3': ['id'], 'h4': ['id'], 'h5': ['id'], 'h6': ['id'],
    'div': ['data-segment-id', 'class'],
    'pre': ['class'], 'code': ['class'],
    'table': ['class'], 'th': ['align'], 'td': ['align']
  },
  
  // 允许的协议
  allowedSchemes: ['http', 'https', 'mailto', 'tel'],
  
  // 图片懒加载设置
  imageDefaults: {
    loading: 'lazy',
    style: 'max-width: 100%; height: auto;'
  }
};

/**
 * 安全的段落渲染器
 * @param content Markdown内容
 * @returns 安全的HTML字符串
 */
export function secureRenderSegment(content: string): string {
  try {
    const processor = unified()
      .use(remarkParse)
      .use(remarkGfm)
      .use(remarkRehype, { 
        allowDangerousHtml: false // 禁用危险HTML
      })
      .use(rehypeHighlight)
      .use(rehypeStringify);

    let result = processor.processSync(content);
    let htmlString = String(result);
    
    // 安全处理步骤
    htmlString = sanitizeHTML(htmlString);
    htmlString = secureExternalLinks(htmlString);
    htmlString = enableImageLazyLoading(htmlString);
    htmlString = addHeadingIds(htmlString);
    
    return htmlString;
  } catch (error) {
    console.error('Secure rendering error:', error);
    return '<p style="color: #e74c3c;">⚠️ 内容渲染错误</p>';
  }
}

/**
 * HTML内容消毒
 * 移除潜在危险的标签和属性
 */
function sanitizeHTML(html: string): string {
  // 简单的HTML标签白名单过滤
  // 在生产环境中，建议使用DOMPurify或类似库
  
  // 移除script标签
  html = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  
  // 移除iframe标签
  html = html.replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '');
  
  // 移除object和embed标签
  html = html.replace(/<(object|embed)\b[^<]*(?:(?!<\/\1>)<[^<]*)*<\/\1>/gi, '');
  
  // 移除危险的事件处理器
  html = html.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '');
  
  // 移除javascript:协议
  html = html.replace(/javascript:/gi, '');
  
  return html;
}

/**
 * 为外部链接添加安全属性
 */
function secureExternalLinks(html: string): string {
  return html.replace(
    /<a\s+([^>]*href\s*=\s*["'])([^"']+)(["'][^>]*)>/gi,
    (match, prefix, url, suffix) => {
      // 检查是否是外部链接
      const isExternal = /^https?:\/\//.test(url) && !url.includes(window.location.hostname);
      
      if (isExternal) {
        // 为外部链接添加安全属性
        let secureAttrs = '';
        if (!match.includes('rel=')) {
          secureAttrs += ' rel="noopener noreferrer"';
        }
        if (!match.includes('target=')) {
          secureAttrs += ' target="_blank"';
        }
        return `<a ${prefix}${url}${suffix}${secureAttrs}>`;
      }
      
      return match;
    }
  );
}

/**
 * 启用图片懒加载
 */
function enableImageLazyLoading(html: string): string {
  return html.replace(
    /<img\s+([^>]*)>/gi,
    (match, attrs) => {
      // 添加懒加载属性
      if (!attrs.includes('loading=')) {
        attrs += ' loading="lazy"';
      }
      
      // 添加默认样式
      if (!attrs.includes('style=')) {
        attrs += ' style="max-width: 100%; height: auto; border-radius: 8px;"';
      }
      
      // 添加错误处理
      if (!attrs.includes('onerror=')) {
        const fallbackSrc = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+PGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iIzk5OSIvPjx0ZXh0IHg9IjEyIiB5PSIxNiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzk5OSI+Pz88L3RleHQ+PC9zdmc+';
        attrs += ` onerror="this.src='${fallbackSrc}'; this.alt='图片加载失败'"`;
      }
      
      return `<img ${attrs}>`;
    }
  );
}

/**
 * 为标题添加ID属性（用于锚点跳转）
 */
function addHeadingIds(html: string): string {
  return html.replace(
    /<(h[1-6])>([^<]+)<\/h[1-6]>/g,
    (match, tag, text) => {
      // 生成安全的ID
      const id = text.toLowerCase()
        .replace(/[^\w\u4e00-\u9fa5\s-]/g, '') // 保留中英文字符、数字、连字符
        .replace(/\s+/g, '-')                    // 空格转连字符
        .replace(/-+/g, '-')                     // 多个连字符合并
        .replace(/^-|-$/g, '');                  // 移除首尾连字符
      
      if (id) {
        return `<${tag} id="${id}">${text}</${tag}>`;
      }
      return match;
    }
  );
}

/**
 * 验证URL是否安全
 */
export function isSecureURL(url: string): boolean {
  try {
    const parsed = new URL(url);
    return SECURITY_CONFIG.allowedSchemes.includes(parsed.protocol.slice(0, -1));
  } catch {
    return false;
  }
}

/**
 * 获取安全配置
 */
export function getSecurityConfig() {
  return { ...SECURITY_CONFIG };
}

/**
 * 生成安全渲染的统计信息
 */
export function generateSecurityReport(html: string): {
  externalLinks: number;
  images: number;
  codeBlocks: number;
  sanitizedElements: number;
} {
  return {
    externalLinks: (html.match(/rel="noopener noreferrer"/g) || []).length,
    images: (html.match(/<img[^>]*loading="lazy"/g) || []).length,
    codeBlocks: (html.match(/<pre[^>]*>/g) || []).length,
    sanitizedElements: 0 // 在实际实现中会计算被移除的危险元素数量
  };
} 