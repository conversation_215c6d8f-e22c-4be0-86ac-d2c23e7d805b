# 🔧 简化M2验证脚本

## 🎯 无依赖验证方法

由于部分模块可能还在加载，这里提供简化的验证脚本：

### 方法一：检查M2系统状态

```javascript
// 简化的M2状态检查
function checkM2Status() {
  console.log('🔍 M2系统状态检查...\n');
  
  // 检查M2调试接口
  if (typeof m2Debug !== 'undefined') {
    console.log('✅ M2调试接口已加载');
    console.log('📊 M2状态:', m2Debug.getStatus());
    console.log('📝 段落数量:', m2Debug.getSegments().length);
    
    // 检查各个组件
    const components = {
      '增量解析器': m2Debug.getIncrementalParser(),
      '滚动管理器': m2Debug.getScrollManager(), 
      '防抖管理器': m2Debug.getDebounceManager(),
      'TOC生成器': m2Debug.getTOCGenerator()
    };
    
    Object.entries(components).forEach(([name, component]) => {
      console.log(`${component ? '✅' : '❌'} ${name}: ${component ? '已加载' : '未加载'}`);
    });
    
  } else {
    console.log('❌ M2调试接口未找到');
    console.log('💡 请刷新页面或检查M2渲染器是否正确加载');
  }
  
  // 检查DOM中的M2标识
  const m2Elements = document.querySelectorAll('[data-segment-id]');
  console.log(`📄 M2段落元素: ${m2Elements.length}个`);
  
  // 检查M2状态指示器
  const statusIndicator = document.querySelector('.fixed.bottom-20.right-4');
  if (statusIndicator) {
    console.log('📋 M2状态指示器:', statusIndicator.textContent);
  }
}

checkM2Status();
```

### 方法二：基础性能检查

```javascript
// 不依赖performanceMonitor的性能检查
function basicPerformanceCheck() {
  console.log('🧪 基础性能检查...\n');
  
  // 检查DOM节点数
  const totalNodes = document.querySelectorAll('*').length;
  console.log(`🌐 DOM节点总数: ${totalNodes}`);
  
  // 检查M2段落
  const segments = document.querySelectorAll('[data-segment-id]');
  console.log(`📄 M2段落数: ${segments.length}`);
  
  // 检查性能API
  if (typeof performance !== 'undefined') {
    console.log('✅ Performance API 可用');
    
    // 简单的渲染时间测试
    console.time('DOM查询性能');
    document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    console.timeEnd('DOM查询性能');
    
  } else {
    console.log('❌ Performance API 不可用');
  }
  
  // 内存使用检查
  if (performance.memory) {
    const memory = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
    console.log(`💾 内存使用: ${memory}MB`);
  }
}

basicPerformanceCheck();
```

### 方法三：M2功能验证

```javascript
// M2功能验证
function verifyM2Functions() {
  console.log('🔧 M2功能验证...\n');
  
  if (typeof m2Debug === 'undefined') {
    console.log('❌ M2系统未加载，请先刷新页面');
    return;
  }
  
  try {
    // 检查防抖管理器
    const debounceManager = m2Debug.getDebounceManager();
    if (debounceManager) {
      const currentDelay = debounceManager.getCurrentDelay();
      console.log(`⏱️ 当前防抖延迟: ${currentDelay}ms`);
      
      const metrics = debounceManager.getLastMetrics();
      if (metrics) {
        console.log(`📏 文档长度: ${metrics.length}字符`);
        console.log(`🧮 复杂元素: ${metrics.complexElementCount}个`);
      }
    }
    
    // 检查TOC生成器
    const tocGenerator = m2Debug.getTOCGenerator();
    if (tocGenerator) {
      const toc = tocGenerator.getLastTOC();
      if (toc) {
        console.log(`📚 TOC标题数: ${toc.flatList.length}个`);
        console.log(`📊 TOC统计:`, tocGenerator.getTOCStats());
      }
    }
    
    // 检查滚动管理器
    const scrollManager = m2Debug.getScrollManager();
    if (scrollManager) {
      const position = scrollManager.getCurrentPosition();
      console.log(`📍 当前滚动位置:`, position ? '已捕获' : '未捕获');
    }
    
    console.log('\n✅ M2功能验证完成');
    
  } catch (error) {
    console.error('❌ M2功能验证失败:', error);
  }
}

verifyM2Functions();
```

## 🔧 问题排查步骤

### 1. 如果M2系统未加载

```javascript
// 检查导入问题
console.log('检查M2相关文件...');
console.log('M2调试接口:', typeof m2Debug);
console.log('M2状态元素:', document.querySelector('.fixed.bottom-20.right-4'));
console.log('M2段落元素:', document.querySelectorAll('[data-segment-id]').length);
```

### 2. 如果仍有性能问题

```javascript
// 手动触发性能优化
function manualOptimization() {
  console.log('🔧 手动优化触发...');
  
  // 清理不必要的元素
  const unnecessaryElements = document.querySelectorAll('.redundant');
  console.log(`🧹 清理${unnecessaryElements.length}个冗余元素`);
  unnecessaryElements.forEach(el => el.remove());
  
  // 强制垃圾回收(如果可用)
  if (window.gc) {
    window.gc();
    console.log('🗑️ 强制垃圾回收完成');
  }
  
  console.log('✅ 手动优化完成');
}

manualOptimization();
```

### 3. 重新启用M2系统

如果M2系统完全没有加载，可以尝试：

1. **硬刷新页面**: `Cmd+Shift+R` (Mac) 或 `Ctrl+Shift+R` (Windows)
2. **清除缓存**: 开发者工具 → Application → Storage → Clear site data
3. **检查控制台**: 查看是否有其他错误信息

## 📞 快速支持命令

```javascript
// 一键诊断命令
function quickDiagnosis() {
  console.log('🔍 快速诊断开始...\n');
  
  checkM2Status();
  console.log('\n' + '='.repeat(50) + '\n');
  
  basicPerformanceCheck();
  console.log('\n' + '='.repeat(50) + '\n');
  
  verifyM2Functions();
  
  console.log('\n📋 诊断完成！请将上述信息提供给技术支持。');
}

// 运行完整诊断
quickDiagnosis();
```

---

**现在请运行上述任一脚本来检查M2系统状态！** 🔧 