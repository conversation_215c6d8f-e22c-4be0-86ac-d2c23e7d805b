````markdown
# 专家第二轮指导 —— 实时模式 P0 实施细案 + 关键代码示例

---

## A. 结论确认

*两位技术员的补充资料**完整满足**我上轮要求的 4 项信息：  
增量解析 API 已具备 `{added|updated|removed}` 结构；  
Next 13 下 Worker-loader 方案已调研；  
虚拟列表拟采用 **@tanstack/react-virtual**，并给出高度估算策略；  
协作暂不纳入范围。  
P0 排期清晰（7 天）且人手到位。  

> 下面给出 **(1) 逐文件改动指引**、**(2) Worker 构建与渲染骨架**、**(3) 无 CodeMirror diff 的增量解析接法**、**(4) 虚拟列表封装示例**、**(5) 性能基准脚本**。如遇阻塞，请在对应事项后 @ 我。

---

## B. 逐文件改动（diff patch 思路）

| 文件 | 动作 | 重点行 |
|------|------|--------|
| **LiveRenderMode.tsx** | ① 把 `useMemo(split+render)` → **`useIncrementalRender()` 自定义 hook**；<br>② 将 `finishEditing()` 改为 *只更新段落数组 + emit `onDocChange(changes)`*，不再重建整文；<br>③ 用 `usePerformanceMonitor()` 包围渲染流程。 | 详见代码片段 §C.1 |
| **incremental-parser.ts** | 暴露 `parseIncremental(newTxt, prevTxt)` overload —— 当 **无 CodeMirror diff** 时用 [diff-match-patch] 行级 diff，回落到受影响行范围。 | 详见 §C.2 |
| **worker/markdown-render.worker.ts** | 新增 Worker 主文件，导出 `renderBatch(changes)`；内部缓存 `Map<hash, html>`；接收 `rev` 号并回传 `{rev, results}`。 | 详见 §C.3 |
| **lib/worker-pool.ts** | 简易池：`size = navigator.hardwareConcurrency/2`；接口 `await pool.renderMarkdown(batch, rev)` | |
| **paragraph-cache.ts** | `const cache = new LRU<string,string>(max=2000)` —— Map 比 WeakMap 合适，Key 为字符串哈希 | |
| **next.config.js** | 增加 `worker-loader` 规则；关掉 Next 图片 loader 404。 | 详见 §C.4 |

---

## C. 关键代码示例

### C.1 `useIncrementalRender`（主线程 hook）

```ts
export function useIncrementalRender(content: string) {
  const workerPool = useMemo(() => new WorkerPool(), []);
  const [blocks, setBlocks] = useState<Block[]>([]);
  const revRef = useRef(0);

  useAdaptiveDebounce(content, async (newContent) => {
    performanceMonitor.markStart('parse');
    const diff = parseIncremental(newContent, prevContentRef.current); // 无 CM diff
    performanceMonitor.markEnd('parse');

    performanceMonitor.markStart('worker');
    const rendered = await workerPool.renderMarkdown(diff.changes, ++revRef.current);
    performanceMonitor.markEnd('worker');

    setBlocks(applyBlockChanges(blocksRef.current, rendered));
    prevContentRef.current = newContent;
  });
  return blocks;
}
````

### C.2 `parseIncremental(newTxt, prevTxt)` overload

```ts
import { diffLines } from 'diff';

export function parseIncremental(newTxt: string, prevTxt: string | null) {
  if (!prevTxt) return forceReparse(newTxt);
  const diff = diffLines(prevTxt, newTxt, { newlineIsToken: true });
  let cursor = 0, changes: SegmentChange[] = [];
  diff.forEach(p => {
    if (p.added) {
      changes.push({ type:'add', content:p.value, startOffset:cursor, endOffset:cursor+p.count! });
      cursor += p.count!;
    } else if (p.removed) {
      changes.push({ type:'remove', content:p.value, startOffset:cursor, endOffset:cursor });
    } else {
      cursor += p.count!;
    }
  });
  return { changes, affectedRange:{start:changes[0]?.startOffset||0, end:cursor} };
}
```

### C.3 Worker 主线程（markdown-render.worker.ts）

```ts
const cache = new Map<string,string>();

self.onmessage = async ({ data }) => {
  if (data.type === 'render') {
    const { batch, rev } = data;
    const results = await Promise.all(batch.map(async block => {
      const key = block.hash;
      if (cache.has(key)) return { id:block.id, html:cache.get(key) };
      const html = await processor.process(block.content).then(f=>String(f));
      cache.set(key, html);
      return { id:block.id, html };
    }));
    self.postMessage({ type:'done', rev, results });
  }
};

// 初始化完成
self.postMessage({ type:'ready' });
```

### C.4 Next.js 13 Worker 构建配置

```js
// next.config.js
const withWorkers = (config = {}) => ({
  ...config,
  webpack(cfg, opts) {
    cfg.module.rules.push({
      test: /\.worker\.ts$/,
      loader: 'worker-loader',
      options: {
        filename: 'static/chunks/[hash].worker.js',
        publicPath: '/_next/',
        esModule: false,
      },
    });
    return typeof config.webpack === 'function'
      ? config.webpack(cfg, opts)
      : cfg;
  },
});
module.exports = withWorkers({
  eslint: { ignoreDuringBuilds: true },
  images: { unoptimized: true },
});
```

> 只需 `worker-loader` 一条即可；`resolve.fallback.fs=false` 属于旧版，Next 13 默认已有。

### C.5 `VirtualParagraphList`（react-virtual 封装）

```tsx
import { useVirtualizer } from '@tanstack/react-virtual';

export function VirtualParagraphList({ blocks }: { blocks: Block[] }) {
  const parentRef = useRef<HTMLDivElement>(null);
  const rowVirtualizer = useVirtualizer({
    count: blocks.length,
    getScrollElement: () => parentRef.current,
    estimateSize: i => estimateItemHeight(blocks[i]),  // 来自技术员策略
    overscan: 8,
  });

  return (
    <div ref={parentRef} style={{ height:'100%', overflow:'auto' }}>
      <div style={{ height: rowVirtualizer.getTotalSize(), position:'relative' }}>
        {rowVirtualizer.getVirtualItems().map(v => {
          const block = blocks[v.index];
          return (
            <div key={block.id} style={{ position:'absolute', top:0, left:0, width:'100%', transform:`translateY(${v.start}px)` }}>
              <ParagraphBlock paragraph={block} />
            </div>
          );
        })}
      </div>
    </div>
  );
}
```

---

## D. 性能基准脚本 (Playwright + Lighthouse)

1. **脚本位置**: `tests/perf/live-mode.spec.ts`
2. **场景**: 打开 10 k 行文档 → 连续输入 100 字符 → 滚动 2000 px
3. **指标阈值** (gate):

   * P95 输入-to-paint < 300 ms
   * 长任务 < 50 ms
   * FPS > 55
   * DOM nodes < 2500
   * Heap 增长 < 10 %

---

## E. 回答技术疑问

| 疑问                          | 解答                                                                                  |
| --------------------------- | ----------------------------------------------------------------------------------- |
| **Worker 构建配置是否正确？**        | 见 §C.4；只需 `worker-loader` 规则；不再需要 `fallback.fs=false`。                              |
| **无 CodeMirror diff 如何增量？** | 采用 `diffLines(prevTxt, newTxt)` 行级 diff；若行数>10 k 建议直接 `forceReparse` 以防 diff O(n²)。 |
| **Hash 是否足够稳定？**            | 用 `fastHash32(markdown.trim()) + ':' + startOffset`；若协作期望稳定跨用户，可追加 `length`。        |
| **WeakMap or Map？**         | 需字符串键，`WeakMap` 只接受对象，故用 **Map + LRU** 更合适。                                         |

---

## F. 下一步行动

1. 技术员按 **P0 表** 执行；关键文件改动可直接复制 §C 代码。
2. 任何构建/类型报错，贴 stacktrace 给我；我会提供精准 patch。
3. 本周内提交 **Perf 报告**（Playwright/Lighthouse excel），作为 M3 终验。

加油，期待你们的指标截图！ 🚀

```

::contentReference[oaicite:0]{index=0}
```
