# Cursor修复实时模式最终方案

## 📋 问题背景

实时模式在长文档（>10,000字符）下出现明显卡顿，影响用户体验。

**症状表现：**
- 输入延迟：200-300ms（P95）
- 段落分割时间：150-200ms
- Markdown渲染时间：300-500ms
- DOM更新时间：200-300ms
- 总响应时间：650-1000ms
- FPS：30-45fps（低于60fps目标）

## 🎯 修复目标

### 性能目标
- P95输入延迟 < 300ms
- 长任务 < 50ms
- FPS > 55
- DOM nodes < 2500
- Heap增长 < 10%

### 功能目标
- 增量渲染：只渲染变化的段落
- Worker渲染：异步处理Markdown
- 虚拟列表：大文档性能优化
- 错误处理：Worker失败时fallback

## 🏗️ 技术架构方案

### 核心组件
```
LiveRenderMode (主组件)
├── useIncrementalRender (增量渲染Hook)
├── WorkerPool (Worker渲染池)
├── VirtualParagraphList (虚拟列表)
└── 性能监控系统
```

### 数据流
```
用户输入 → 自适应防抖 → 增量解析 → Worker渲染 → 虚拟列表 → DOM更新
```

## 📦 技术栈升级

### 新增依赖
```bash
npm install worker-loader@^3.0.8 diff@^5.2.0 @tanstack/react-virtual@^3.0.0 lru-cache@^10
npm install --save-dev @types/diff
```

### 文件结构
```
src/
├── lib/
│   ├── useAdaptiveDebounce.ts    # 自适应防抖Hook
│   ├── worker-pool.ts            # Worker池管理
│   ├── applyBlockChanges.ts      # 块变更应用
│   └── patchBlock.ts             # 块内容修补
├── worker/
│   └── markdown-render.worker.ts # Markdown渲染Worker
├── types/
│   └── worker.d.ts               # Worker类型声明
└── components/editor/
    └── LiveRenderMode.tsx        # 主组件改造
```

## 🔧 核心实现方案

### 1. Block数据结构
```typescript
export interface Block {
  id: string;           // `${hash32(content.trim())}:${startOffset}`
  content: string;      // 段落原始Markdown文本
  hash: string;         // 32-bit hash of content.trim()
  startOffset: number;  // 段内绝对偏移
  endOffset: number;    // 段内绝对偏移
  html?: string;        // Worker渲染后的HTML
  dirty?: boolean;      // 本次编辑是否受影响
  type?: 'paragraph' | 'heading' | 'listItem' | 'codeFence' | 'html';
}

export interface RenderResult {
  id: string;           // = block.id
  html?: string;        // add/update时必须；remove可省
  type: 'add' | 'update' | 'remove';
  hash?: string;        // Worker侧可返回新hash
}
```

### 2. 增量渲染Hook
```typescript
export function useIncrementalRender(content: string) {
  const workerPool = useMemo(() => new WorkerPool(), []);
  const [blocks, setBlocks] = useState<Block[]>([]);
  const revRef = useRef(0);

  useAdaptiveDebounce(content, async (newContent) => {
    performanceMonitor.markStart('parse');
    const diff = parseIncremental(newContent, prevContentRef.current);
    performanceMonitor.markEnd('parse');

    performanceMonitor.markStart('worker');
    const rendered = await workerPool.renderMarkdown(diff.changes, ++revRef.current);
    performanceMonitor.markEnd('worker');

    setBlocks(applyBlockChanges(blocksRef.current, rendered));
    prevContentRef.current = newContent;
  });
  return blocks;
}
```

### 3. Worker池管理
```typescript
export class WorkerPool {
  private pool: Worker[] = [];
  private queue: (() => void)[] = [];
  
  constructor(size = Math.max(1, (navigator.hardwareConcurrency || 4) >> 1)) {
    for (let i = 0; i < size; i++) this.pool.push(this.spawn());
  }
  
  private spawn() {
    const w = new Worker(new URL('@/worker/markdown-render.worker.ts', import.meta.url));
    w.onerror = () => { 
      console.warn('Worker crashed, respawn'); 
      this.pool.splice(this.pool.indexOf(w),1); 
      this.pool.push(this.spawn()); 
    };
    return w;
  }
  
  async renderMarkdown(batch: Block[], rev: number) {
    return new Promise<RenderResult[]>((res, rej) => {
      const work = () => {
        const w = this.pool.pop();
        if (!w) { this.queue.push(work); return; }
        w.onmessage = ({data}) => { 
          this.pool.push(w!); 
          res(data.results); 
          if (this.queue.length) this.queue.shift()!(); 
        };
        w.postMessage({ type:'render', batch, rev });
      };
      work();
    });
  }
  
  destroy() { this.pool.forEach(w => w.terminate()); this.pool.length = 0; }
}
```

### 4. 虚拟列表实现
```typescript
export function VirtualParagraphList({ blocks }: { blocks: Block[] }) {
  const parentRef = useRef<HTMLDivElement>(null);
  const rowVirtualizer = useVirtualizer({
    count: blocks.length,
    getScrollElement: () => parentRef.current,
    estimateSize: i => estimateItemHeight(blocks[i]),
    overscan: 8,
  });

  return (
    <div ref={parentRef} style={{ height:'100%', overflow:'auto' }}>
      <div style={{ height: rowVirtualizer.getTotalSize(), position:'relative' }}>
        {rowVirtualizer.getVirtualItems().map(v => {
          const block = blocks[v.index];
          return (
            <div key={block.id} style={{ 
              position:'absolute', 
              top:0, 
              left:0, 
              width:'100%', 
              transform:`translateY(${v.start}px)` 
            }}>
              <ParagraphBlock paragraph={block} />
            </div>
          );
        })}
      </div>
    </div>
  );
}
```

### 5. 增量解析算法
```typescript
import { diffLines } from 'diff';

export function parseIncremental(newTxt: string, prevTxt: string | null) {
  if (!prevTxt) return forceReparse(newTxt);
  
  const MAX_DIFF_LINES = 10_000;
  const lineCount = newTxt.split('\n').length;
  if (lineCount > MAX_DIFF_LINES) return forceReparse(newTxt);
  
  const diff = diffLines(prevTxt, newTxt, { newlineIsToken: true });
  let cursor = 0, changes: SegmentChange[] = [];
  
  diff.forEach(p => {
    if (p.added) {
      changes.push({ 
        type:'add', 
        content:p.value, 
        startOffset:cursor, 
        endOffset:cursor+p.count! 
      });
      cursor += p.count!;
    } else if (p.removed) {
      changes.push({ 
        type:'remove', 
        content:p.value, 
        startOffset:cursor, 
        endOffset:cursor 
      });
    } else {
      cursor += p.count!;
    }
  });
  
  return { 
    changes, 
    affectedRange:{start:changes[0]?.startOffset||0, end:cursor} 
  };
}
```

### 6. 错误处理策略
```typescript
const TIMEOUT_MS = 2000;

function renderWithTimeout(batch: Block[], rev: number, pool: WorkerPool) {
  return Promise.race([
    pool.renderMarkdown(batch, rev),
    new Promise<never>((_, rej) =>
      setTimeout(() => rej(new Error('timeout')), TIMEOUT_MS)
    ),
  ]).catch(err => {
    perf.markTag('worker-timeout');
    console.warn('Worker fallback:', err.message);
    return renderMarkdownOnMainThread(batch);
  });
}
```

### 7. 虚拟列表启用条件
```typescript
const SHOULD_VIRTUAL =
  blocks.length > 1500 ||         // 超1500段
  domNodeCount > 2500 ||          // DOM > 2500
  measuredFPS < 50;               // FPS < 50

const [useVirtual, setUseVirtual] = useState(SHOULD_VIRTUAL);
```

## 🔧 配置更新

### Next.js配置
```javascript
// next.config.js
const withWorkers = (config = {}) => ({
  ...config,
  webpack(cfg, opts) {
    cfg.module.rules.push({
      test: /\.worker\.ts$/,
      loader: 'worker-loader',
      options: {
        filename: 'static/chunks/[hash].worker.js',
        publicPath: '/_next/',
        esModule: false,
      },
    });
    return typeof config.webpack === 'function'
      ? config.webpack(cfg, opts)
      : cfg;
  },
});

module.exports = withWorkers({
  eslint: { ignoreDuringBuilds: true },
  images: { unoptimized: true },
});
```

### TypeScript声明
```typescript
// types/worker.d.ts
declare module '*.worker.ts' {
  class WebpackWorker extends Worker {
    constructor();
  }
  export default WebpackWorker;
}
```

## 📊 性能监控

### 监控指标
```typescript
// 性能监控埋点
perf.markStart('parse');    // 增量解析
perf.markStart('worker');   // Worker渲染
perf.markStart('dom');      // DOM更新
perf.markEnd('parse');
perf.markEnd('worker');
perf.markEnd('dom');
```

### 监控策略
- **development**：console + Performance API采样
- **production**：收集指标但不打印，通过`window.__PERF__`暴露

## 🚀 实施计划

### Day 1-2: 基础架构搭建
- [ ] 安装依赖包
- [ ] 配置Worker构建
- [ ] 创建基础文件结构
- [ ] 实现核心Hook

### Day 3-4: Worker渲染实现
- [ ] 实现`markdown-render.worker.ts`
- [ ] 实现`worker-pool.ts`
- [ ] 测试Worker渲染性能
- [ ] 集成错误处理

### Day 5-6: 增量解析集成
- [ ] 实现`parseIncremental(newTxt, prevTxt)`
- [ ] 集成`useIncrementalRender` Hook
- [ ] 测试增量解析效果
- [ ] 优化性能

### Day 7: 虚拟列表集成
- [ ] 实现`VirtualParagraphList`
- [ ] 集成性能监控
- [ ] 性能测试和调优
- [ ] 最终验收

## 📋 专家指导确认

### 技术方案完全理解
- **依赖版本**：worker-loader@^3.0.8、diff@^5.2.0、@tanstack/react-virtual@^3.0.0、lru-cache@^10
- **核心Hook**：`useAdaptiveDebounce` 复用现有 `AdaptiveDebounceManager`
- **Worker池**：`WorkerPool` 负载均衡 + 自动respawn
- **增量解析**：`diffLines` + 10k行阈值
- **虚拟列表**：`@tanstack/react-virtual` + FPS监控

### 专家时间线确认
| Day | 交付内容 | 专家动作 |
|-----|----------|----------|
| **3** | 基础分支 + Worker构建OK | inline review / patch建议 |
| **5** | Playwright trace + Lighthouse | 指标复核 |
| **7** | P0性能门槛达标 | 出具✅完成确认 |

## 🎯 预期效果

### 性能提升
- **输入延迟**：从200-300ms降至<100ms
- **渲染时间**：从300-500ms降至<50ms
- **DOM更新**：从200-300ms降至<30ms
- **总响应时间**：从650-1000ms降至<200ms
- **FPS**：从30-45fps提升至>55fps

### 功能增强
- **增量渲染**：只渲染变化的段落，大幅减少计算量
- **异步处理**：Worker渲染不阻塞主线程
- **虚拟列表**：大文档性能优化
- **错误恢复**：Worker失败时自动fallback

---

**所有技术细节已完全确认，准备开始实施P0快修方案。** 