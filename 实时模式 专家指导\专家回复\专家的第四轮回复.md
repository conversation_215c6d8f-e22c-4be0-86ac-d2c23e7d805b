````markdown
# 专家第四轮确认 & 实施细则  
（包含 **所有待确认点** 的最终答案，直接抄给两位技术员即可）

---

## 1 ️⃣ Block / RenderResult 接口 —— 最终版

```ts
/** 编辑器内部统一的块结构（Markdown ↔ HTML ↔ 协作） */
export interface Block {
  /** 稳定主键：`${hash32(content.trim())}:${startOffset}` */
  id: string;
  /** 段落原始 Markdown 文本 */
  content: string;
  /** 32-bit hash of `content.trim()`（加速 diff & cache） */
  hash: string;
  /** 段内绝对偏移（含行终止符）；用于排序 / 二分定位 */
  startOffset: number;
  endOffset: number;

  /* —— 以下字段运行时可选 —— */
  /** Worker 渲染后的 HTML（缓存可命中时直接复用） */
  html?: string;
  /** 本次编辑是否受影响（脏块） */
  dirty?: boolean;
  /** 语义类型：段落 / 标题 / 列表 / 代码等 */
  type?: 'paragraph' | 'heading' | 'listItem' | 'codeFence' | 'html';
}

export interface RenderResult {
  id: string;           // = block.id
  html?: string;        // add / update 时必须；remove 可省
  type: 'add' | 'update' | 'remove';
  hash?: string;        // Worker 侧可返回新 hash，主线程更新 cache
}
````

> *说明*：`type` 字段在 Block 中**可选**（后续 AI/协作需求更好扩展）；RenderResult 不需要 `startOffset/endOffset`——主线程通过 `id` 找旧块，再 patch。

---

## 2 ️⃣ Worker 文件组织 & TS 配置

| 项           | 建议值                                                                         | 备注                                    |
| ----------- | --------------------------------------------------------------------------- | ------------------------------------- |
| Worker 源码目录 | `src/worker/markdown-render.worker.ts`                                      | 避免放 `public/`；防止被 Next 静态服务绕过 webpack |
| 导入路径        | `import MarkdownWorker from '@/worker/markdown-render.worker.ts';`          | `@` alias 指向 `src`                    |
| tsconfig    | `json\n{ \"compilerOptions\": { \"types\": [\"./types/worker.d.ts\"] } }\n` | 保证全局能识别 `*.worker.ts`                 |
| next.config | 已给出的 `worker-loader` 规则即可                                                   | 无需额外 alias                            |

---

## 3 ️⃣ Worker错误 & 2 s 超时实现

```ts
// 主线程调用
const TIMEOUT_MS = 2000;

function renderWithTimeout(batch: Block[], rev: number, pool: WorkerPool) {
  return Promise.race([
    pool.renderMarkdown(batch, rev),               // 正常渲染
    new Promise<never>((_, rej) =>
      setTimeout(() => rej(new Error('timeout')), TIMEOUT_MS)
    ),
  ]).catch(err => {
    perf.markTag('worker-timeout');
    console.warn('Worker fallback:', err.message);
    return renderMarkdownOnMainThread(batch);      // 同步降级
  });
}
```

*`perf.markTag()`*：如 `PerformanceMonitor` 暂无此 API，直接实现为

```ts
export function markTag(tag: string) {
  if (process.env.NODE_ENV !== 'production') console.warn('[PERF]', tag);
}
```

> WorkerPool 已在 `onerror` & `terminate()` 里自动 respawn；超时则由调用方 fallback。

---

## 4 ️⃣ LiveRenderMode 改造入口

```tsx
export default function LiveRenderMode({ content, onDocChange }: Props) {
  /** ① 增量渲染 Hook */
  const blocks = useIncrementalRender(content, onDocChange);

  /** ② 编辑单块时，直接触发 onDocChange(newContent) —— */
  return (
    <VirtualParagraphList blocks={blocks}>
      {(block) => (
        <ParagraphBlock
          key={block.id}
          block={block}
          onEdit={(md) => {
            onDocChange?.(patchBlock(content, block, md)); // 返回全文但只改一段
          }}
        />
      )}
    </VirtualParagraphList>
  );
}

/* utils/patchBlock.ts —— 用 startOffset/endOffset 替换区间 */
export function patchBlock(doc: string, oldBlock: Block, newMd: string) {
  return (
    doc.slice(0, oldBlock.startOffset) +
    newMd +
    doc.slice(oldBlock.endOffset)
  );
}
```

*`finishEditing`*：可删除；逻辑已拆入 `patchBlock`。

---

## 5 ️⃣ 性能监控策略

| 环境            | 行为                                                               |
| ------------- | ---------------------------------------------------------------- |
| `development` | 打印 console + Performance API 采样                                  |
| `production`  | **仍收集指标**（长任务、latency、heap）但不打印；通过 `window.__PERF__` 钩子暴露给内部调试页面 |

```ts
const isDev = process.env.NODE_ENV !== 'production';
if (isDev) console.log(…);
else if (window.__PERF__) window.__PERF__.push(record);
```

---

## 6 ️⃣ 虚拟列表启用条件

```ts
const SHOULD_VIRTUAL =
  blocks.length > 1500 ||         // 超 1500 段
  domNodeCount > 2500 ||          // DOM > 2500
  measuredFPS < 50;               // FPS < 50

const [useVirtual, setUseVirtual] = useState(SHOULD_VIRTUAL);
```

> 3 个条件任一满足即启用；后续滚动监控若 FPS 恢复亦可自动关闭。

---

## 7 ️⃣ 关键确认回答一览

| # | 提问               | 答案                                    |
| - | ---------------- | ------------------------------------- |
| 1 | Block 字段是否完整？    | **完整**，见 §1。`type` 可选；`hash` 必需。      |
| 2 | RenderResult 字段？ | 见 §1；`type/id/html/hash` 足矣。          |
| 3 | Worker 超时/重启？    | 见 §3；`Promise.race` + `pool.onError`。 |
| 4 | `perf.markTag`？  | 可按 §3 实现，或已存在同名方法。                    |
| 5 | `Virtual` 启用策略？  | 见 §6；三条件任一触发。                         |
| 6 | `tsconfig` 需改吗？  | 仅需 `types/worker.d.ts` 声明；无其他特殊配置。    |

---

## 8 ️⃣ 最终节点 & 时间线

| Day   | 交付                            | 专家动作                     |
| ----- | ----------------------------- | ------------------------ |
| **3** | 基础分支 + Worker 构建 OK           | inline review / patch 建议 |
| **5** | Playwright trace + Lighthouse | 指标复核                     |
| **7** | P0 性能门槛达标                     | 出具 ✅ 完成确认                |

**祝编码顺利！如遇 StackTrace / 构建报错 / 指标不达标，立即贴日志给我。**

```
::contentReference[oaicite:0]{index=0}
```
