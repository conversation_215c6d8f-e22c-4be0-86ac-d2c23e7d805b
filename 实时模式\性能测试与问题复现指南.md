# 实时模式性能测试与问题复现指南

## 📋 测试环境准备

### 1. 开发环境要求
- Node.js 18+
- Chrome/Edge 浏览器 (支持Performance API)
- 开发者工具 Performance 面板
- React DevTools Profiler

### 2. 性能监控启用
```bash
# 确保在开发环境运行
npm run dev

# 打开浏览器开发者工具
# 1. Performance 面板
# 2. Console 面板 (查看性能日志)
# 3. Memory 面板 (监控内存使用)
```

## 🧪 问题复现步骤

### 1. 基础卡顿复现

**步骤**:
1. 打开网站，切换到"实时"模式
2. 创建或打开一个长文档 (建议500+段落)
3. 在文档中间位置点击任意段落进入编辑
4. 输入文字观察响应延迟
5. 按Ctrl+Enter保存，观察卡顿时间

**预期现象**:
- 点击段落后延迟1-3秒才进入编辑状态
- 输入时有明显的延迟和卡顿
- 保存时整个页面冻结2-5秒

### 2. 长文档生成脚本

```javascript
// 在浏览器Console中执行，生成测试用长文档
function generateLongDocument(paragraphCount = 1000) {
  const paragraphs = [];
  
  for (let i = 1; i <= paragraphCount; i++) {
    const type = i % 10;
    let content = '';
    
    switch (type) {
      case 0:
        content = `# 标题 ${i}\n\n这是第${i}个标题段落，包含一些基本的文本内容。`;
        break;
      case 1:
        content = `## 二级标题 ${i}\n\n这是一个二级标题，用于测试标题渲染性能。`;
        break;
      case 2:
        content = `\`\`\`javascript\n// 代码块 ${i}\nfunction test${i}() {\n  console.log('测试代码块渲染性能');\n  return ${i};\n}\n\`\`\``;
        break;
      case 3:
        content = `- 列表项 ${i}-1\n- 列表项 ${i}-2\n- 列表项 ${i}-3\n  - 嵌套列表项 ${i}-3-1\n  - 嵌套列表项 ${i}-3-2`;
        break;
      case 4:
        content = `> 引用块 ${i}\n> \n> 这是一个引用块，用于测试引用渲染性能。\n> 包含多行内容。`;
        break;
      case 5:
        content = `| 表格 ${i} | 列1 | 列2 | 列3 |\n|---------|-----|-----|-----|\n| 行1 | 数据1 | 数据2 | 数据3 |\n| 行2 | 数据4 | 数据5 | 数据6 |`;
        break;
      default:
        content = `这是第${i}个普通段落。包含一些**粗体文本**和*斜体文本*，以及[链接](https://example.com)。这个段落用于测试基础的Markdown渲染性能，包含足够的内容来模拟真实的使用场景。`;
    }
    
    paragraphs.push(content);
  }
  
  return paragraphs.join('\n\n');
}

// 生成1000段落的测试文档
const testDocument = generateLongDocument(1000);
console.log(`生成了 ${testDocument.split('\n\n').length} 个段落的测试文档`);
console.log(`文档总长度: ${testDocument.length} 字符`);

// 复制到剪贴板
navigator.clipboard.writeText(testDocument).then(() => {
  console.log('测试文档已复制到剪贴板，可以粘贴到编辑器中');
});
```

### 3. 性能指标监控

**在Console中执行**:
```javascript
// 监控实时模式性能
function monitorLiveMode() {
  const startTime = performance.now();
  let renderCount = 0;
  let totalRenderTime = 0;
  
  // 监控DOM变化
  const observer = new MutationObserver((mutations) => {
    renderCount++;
    const renderTime = performance.now() - startTime;
    totalRenderTime += renderTime;
    
    console.log(`渲染 #${renderCount}: ${renderTime.toFixed(2)}ms`);
    
    if (renderCount % 10 === 0) {
      console.log(`平均渲染时间: ${(totalRenderTime / renderCount).toFixed(2)}ms`);
    }
  });
  
  // 监控实时模式容器
  const liveContainer = document.querySelector('.editor-content');
  if (liveContainer) {
    observer.observe(liveContainer, {
      childList: true,
      subtree: true,
      attributes: true
    });
    console.log('开始监控实时模式性能...');
  } else {
    console.error('未找到实时模式容器');
  }
  
  return observer;
}

// 开始监控
const performanceObserver = monitorLiveMode();

// 停止监控 (在测试完成后执行)
// performanceObserver.disconnect();
```

## 📊 性能基准测试

### 1. 渲染性能测试

**测试用例**:
```javascript
// 测试不同段落数量的渲染性能
async function testRenderingPerformance() {
  const testCases = [100, 300, 500, 1000, 2000];
  const results = [];
  
  for (const paragraphCount of testCases) {
    console.log(`测试 ${paragraphCount} 段落...`);
    
    const testDoc = generateLongDocument(paragraphCount);
    const startTime = performance.now();
    
    // 模拟内容变化 (需要在实时模式下执行)
    // 这里需要手动触发内容更新
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    results.push({
      paragraphCount,
      renderTime,
      avgTimePerParagraph: renderTime / paragraphCount
    });
    
    console.log(`${paragraphCount} 段落渲染耗时: ${renderTime.toFixed(2)}ms`);
    console.log(`平均每段落: ${(renderTime / paragraphCount).toFixed(2)}ms`);
  }
  
  return results;
}
```

### 2. 内存使用测试

**测试脚本**:
```javascript
// 监控内存使用情况
function monitorMemoryUsage() {
  if (!performance.memory) {
    console.warn('浏览器不支持内存监控');
    return;
  }
  
  const initialMemory = performance.memory.usedJSHeapSize;
  console.log(`初始内存使用: ${(initialMemory / 1024 / 1024).toFixed(2)} MB`);
  
  const interval = setInterval(() => {
    const currentMemory = performance.memory.usedJSHeapSize;
    const memoryIncrease = currentMemory - initialMemory;
    
    console.log(`当前内存: ${(currentMemory / 1024 / 1024).toFixed(2)} MB`);
    console.log(`内存增长: ${(memoryIncrease / 1024 / 1024).toFixed(2)} MB`);
    
    if (memoryIncrease > 50 * 1024 * 1024) { // 50MB
      console.warn('⚠️ 内存使用过高，可能存在内存泄漏');
    }
  }, 5000);
  
  return interval;
}

// 开始监控内存
const memoryMonitor = monitorMemoryUsage();

// 停止监控 (测试完成后)
// clearInterval(memoryMonitor);
```

## 🔍 问题诊断清单

### 1. 性能瓶颈检查

**检查项目**:
- [ ] 段落分割算法耗时 (目标: <50ms)
- [ ] Markdown渲染耗时 (目标: 每段落<1ms)
- [ ] React组件渲染耗时 (目标: <100ms)
- [ ] DOM更新耗时 (目标: <200ms)
- [ ] 内存使用增长 (目标: <10MB/1000段落)

**诊断命令**:
```javascript
// 检查当前实时模式状态
function diagnoseLiveMode() {
  const container = document.querySelector('.editor-content');
  const paragraphs = container?.querySelectorAll('[data-paragraph-index]');
  
  console.log('=== 实时模式诊断 ===');
  console.log(`段落数量: ${paragraphs?.length || 0}`);
  console.log(`DOM节点数: ${document.querySelectorAll('*').length}`);
  console.log(`容器高度: ${container?.scrollHeight || 0}px`);
  
  if (performance.memory) {
    console.log(`内存使用: ${(performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
  }
  
  // 检查是否有长任务
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.duration > 50) {
          console.warn(`长任务检测: ${entry.duration.toFixed(2)}ms`);
        }
      }
    });
    observer.observe({ entryTypes: ['longtask'] });
  }
}

diagnoseLiveMode();
```

### 2. 对比测试

**与其他模式对比**:
1. 在相同文档下测试"源码"模式性能
2. 在相同文档下测试"预览"模式性能
3. 在相同文档下测试"分屏"模式性能
4. 记录各模式的响应时间和资源使用

**对比指标**:
- 初始加载时间
- 编辑响应延迟
- 滚动流畅度
- 内存使用量
- CPU使用率

## 🎯 测试结果记录模板

```
=== 实时模式性能测试报告 ===
测试时间: [日期时间]
测试环境: [浏览器版本/操作系统]
文档规模: [段落数量/字符数]

性能指标:
- 初始渲染时间: ___ms
- 段落编辑响应: ___ms
- 保存操作耗时: ___ms
- 滚动流畅度: ___fps
- 内存使用峰值: ___MB

问题现象:
- [ ] 点击段落延迟
- [ ] 输入卡顿
- [ ] 保存冻结
- [ ] 滚动不流畅
- [ ] 内存持续增长

对比数据:
- 源码模式: ___ms
- 预览模式: ___ms
- 分屏模式: ___ms
```

---

**注**: 此指南用于专家分析问题时的测试和验证，请按照步骤进行系统性的性能测试。
