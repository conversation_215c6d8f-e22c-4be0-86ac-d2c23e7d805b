# 🍎 Mac用户 M1性能验收指南

## 🚀 快速开始 (Mac专用)

### 方法一：顶部快捷测试按钮 ⭐ 推荐

1. **访问**: http://localhost:3000
2. **查找测试按钮**: 页面顶部中央有一个蓝色的 "🧪 M1性能测试" 按钮
3. **一键测试**: 点击按钮，等待1-2秒
4. **查看结果**: 
   - 🎉 绿色 = 验收通过
   - ⚠️ 红色 = 需要优化

### 方法二：Mac快捷键

**正确的Mac快捷键**：
- **测试面板**: `Cmd+Shift+T` (不是Ctrl+Shift+T)
- **性能面板**: `Cmd+Shift+P` (不是Ctrl+Shift+P)

### 方法三：控制台验收

1. **打开开发者工具**: `Cmd+Option+I`
2. **切换到Console**: 点击Console标签
3. **运行测试脚本**:

```javascript
// 一键验收测试
function runM1Acceptance() {
  const metrics = performanceMonitor.getMetrics();
  console.log('🧪 M1阶段验收测试开始...\n');
  
  const results = {
    fps: { pass: metrics.fps >= 50, value: metrics.fps, target: '≥50' },
    latency: { pass: metrics.inputLatency.p95 < 100, value: metrics.inputLatency.p95, target: '<100ms' },
    longTask: { pass: metrics.longTasks.maxDuration < 80, value: metrics.longTasks.maxDuration, target: '<80ms' },
    domNodes: { pass: metrics.domNodeCount <= 2500, value: metrics.domNodeCount, target: '≤2500' }
  };
  
  Object.entries(results).forEach(([key, result]) => {
    const status = result.pass ? '✅' : '❌';
    console.log(`${status} ${key}: ${result.value} (目标: ${result.target})`);
  });
  
  const allPassed = Object.values(results).every(r => r.pass);
  console.log(`\n📊 总体结果: ${allPassed ? '✅ 验收通过' : '❌ 验收失败'}`);
  
  return allPassed;
}

// 执行测试
runM1Acceptance();
```

## 🔧 Mac专属测试方法

### Safari浏览器测试

如果使用Safari：
1. **启用开发菜单**: Safari → 偏好设置 → 高级 → 显示开发菜单
2. **打开Web检查器**: `Cmd+Option+I`
3. **运行测试**: 使用顶部测试按钮或控制台命令

### Chrome浏览器测试

1. **打开DevTools**: `Cmd+Option+I`
2. **Performance面板**: 
   - 点击录制按钮
   - 在编辑器中输入大段文字
   - 停止录制，查看FPS图表

### 实际功能测试

1. **输入响应测试**:
   - 在编辑器中快速输入文字
   - 观察右侧预览更新是否流畅
   - **预期**: 无卡顿，响应流畅

2. **滚动性能测试**:
   - 创建长文档（复制粘贴大段内容）
   - 使用触控板双指滚动
   - **预期**: 滚动顺滑，FPS ≥ 50

3. **段落稳定性测试**:
   ```javascript
   // 在控制台检查段落ID稳定性
   Array.from(document.querySelectorAll('[data-segment-id]'))
     .map(el => el.getAttribute('data-segment-id'))
     .forEach(id => console.log('段落ID:', id));
   ```

## 📊 验收成功标准

当看到以下结果时表示验收通过：

### 快捷测试按钮显示
```
🎉 M1验收通过 (55.0fps, 85.2ms)
```

### 控制台详细结果
```
🧪 M1阶段验收测试开始...

✅ fps: 55.0 (目标: ≥50)
✅ latency: 85.2 (目标: <100ms)
✅ longTask: 45.1 (目标: <80ms)
✅ domNodes: 1250 (目标: ≤2500)

📊 总体结果: ✅ 验收通过
```

## 🐛 Mac常见问题

### 问题1：快捷键不响应
**原因**: 使用了错误的键位组合
**解决**: 
- ❌ 错误: `Ctrl+Shift+T`
- ✅ 正确: `Cmd+Shift+T`

### 问题2：测试按钮不显示
**检查**:
```javascript
// 确认开发环境
console.log('Environment:', process.env.NODE_ENV);
// 应该显示 'development'
```

**解决**: 确保使用 `npm run dev` 启动开发服务器

### 问题3：Safari性能面板功能受限
**原因**: Safari的PerformanceObserver支持有限
**解决**: 
1. 使用Chrome浏览器进行测试
2. 或使用手动功能测试方法

### 问题4：触控板滚动测试
**优化测试方法**:
1. 禁用其他应用以减少干扰
2. 使用Activity Monitor检查CPU占用
3. 确保MacBook已连接电源（避免节能模式）

## 🎯 Mac系统专属验收清单

- [ ] ✅ 顶部测试按钮可见且可点击
- [ ] ✅ `Cmd+Shift+T` 快捷键正常工作
- [ ] ✅ 控制台验收脚本运行正常
- [ ] ✅ 触控板滚动流畅无卡顿
- [ ] ✅ 输入响应迅速
- [ ] ✅ 所有4项性能指标达标

## 🚀 验收通过后

看到验收通过结果后，请告诉我：
- ✅ 验收通过，可以进入M2阶段
- ❌ 验收失败，需要继续优化

我们将进入M2阶段的更深度优化！

---

**Mac用户专属提示**: 如果遇到任何问题，优先使用顶部的蓝色测试按钮，这是最简单可靠的验收方法！ 🍎 