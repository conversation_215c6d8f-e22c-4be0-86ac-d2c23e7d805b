interface DocumentMetrics {
  length: number;
  lineCount: number;
  complexElementCount: number; // 代码块、表格、公式等复杂元素数量
  estimatedRenderTime: number; // 预估渲染时间（毫秒）
}

interface DebounceConfig {
  minDelay: number;
  maxDelay: number;
  baseDelay: number;
  complexityMultiplier: number;
  performanceMultiplier: number;
}

export class AdaptiveDebounceManager {
  private config: DebounceConfig;
  private lastMetrics: DocumentMetrics | null = null;
  private currentDelay: number;
  private performanceHistory: number[] = [];
  private maxHistorySize = 10;

  constructor(config?: Partial<DebounceConfig>) {
    this.config = {
      minDelay: 50,      // 最小防抖时间
      maxDelay: 500,     // 最大防抖时间
      baseDelay: 120,    // 基础防抖时间
      complexityMultiplier: 1.5, // 复杂度乘数
      performanceMultiplier: 1.2, // 性能乘数
      ...config
    };
    
    this.currentDelay = this.config.baseDelay;
  }

  /**
   * 分析文档特征，计算自适应防抖时间
   */
  calculateDebounceDelay(content: string, lastRenderTime?: number): number {
    const metrics = this.analyzeDocument(content);
    
    // 记录渲染性能
    if (lastRenderTime !== undefined) {
      this.recordPerformance(lastRenderTime);
    }

    // 计算基于文档复杂度的延迟
    let delay = this.config.baseDelay;

    // 1. 基于文档长度调整
    const lengthFactor = this.calculateLengthFactor(metrics.length);
    delay *= lengthFactor;

    // 2. 基于复杂元素调整
    const complexityFactor = this.calculateComplexityFactor(metrics.complexElementCount);
    delay *= complexityFactor;

    // 3. 基于历史性能调整
    const performanceFactor = this.calculatePerformanceFactor();
    delay *= performanceFactor;

    // 4. 基于变化频率调整（如果有历史数据）
    const changeFactor = this.calculateChangeFactor(metrics);
    delay *= changeFactor;

    // 限制在合理范围内
    delay = Math.max(this.config.minDelay, Math.min(this.config.maxDelay, delay));

    this.currentDelay = Math.round(delay);
    this.lastMetrics = metrics;

    console.log(`AdaptiveDebounce: ${this.currentDelay}ms (length: ${lengthFactor.toFixed(2)}, complexity: ${complexityFactor.toFixed(2)}, perf: ${performanceFactor.toFixed(2)})`);

    return this.currentDelay;
  }

  private analyzeDocument(content: string): DocumentMetrics {
    const lines = content.split('\n');
    const lineCount = lines.length;

    // 计算复杂元素数量
    let complexElementCount = 0;
    
    // 代码块
    complexElementCount += (content.match(/```[\s\S]*?```/g) || []).length;
    complexElementCount += (content.match(/`[^`]+`/g) || []).length * 0.2; // 行内代码权重较低
    
    // 表格
    complexElementCount += (content.match(/\|.*\|/g) || []).length * 0.5;
    
    // 数学公式
    complexElementCount += (content.match(/\$\$[\s\S]*?\$\$/g) || []).length * 2; // 公式渲染复杂
    complexElementCount += (content.match(/\$[^$]+\$/g) || []).length * 0.5;
    
    // 图片和链接
    complexElementCount += (content.match(/!\[.*?\]\(.*?\)/g) || []).length * 0.3;
    complexElementCount += (content.match(/\[.*?\]\(.*?\)/g) || []).length * 0.1;
    
    // 列表项
    complexElementCount += (content.match(/^[\s]*[-*+]\s/gm) || []).length * 0.1;
    complexElementCount += (content.match(/^[\s]*\d+\.\s/gm) || []).length * 0.1;

    // 标题
    complexElementCount += (content.match(/^#+\s/gm) || []).length * 0.2;

    // 预估渲染时间（基于经验公式）
    const estimatedRenderTime = 
      lineCount * 0.1 + // 基础行数时间
      complexElementCount * 2 + // 复杂元素时间
      content.length * 0.001; // 文本长度时间

    return {
      length: content.length,
      lineCount,
      complexElementCount: Math.round(complexElementCount),
      estimatedRenderTime
    };
  }

  private calculateLengthFactor(length: number): number {
    // 分段式长度因子
    if (length < 1000) return 0.8;        // 短文档：快速响应
    if (length < 5000) return 1.0;        // 中等文档：标准响应
    if (length < 20000) return 1.3;       // 长文档：适当延迟
    if (length < 50000) return 1.6;       // 很长文档：更多延迟
    return 2.0;                           // 超长文档：最大延迟
  }

  private calculateComplexityFactor(complexElementCount: number): number {
    // 复杂元素越多，需要更多时间
    if (complexElementCount < 5) return 1.0;
    if (complexElementCount < 20) return 1.2;
    if (complexElementCount < 50) return 1.4;
    return 1.6;
  }

  private calculatePerformanceFactor(): number {
    if (this.performanceHistory.length < 3) return 1.0;

    // 计算平均渲染时间
    const avgRenderTime = this.performanceHistory.reduce((a, b) => a + b, 0) / this.performanceHistory.length;
    
    // 基于性能调整
    if (avgRenderTime < 50) return 0.9;    // 性能很好：减少延迟
    if (avgRenderTime < 100) return 1.0;   // 性能正常：标准延迟
    if (avgRenderTime < 200) return 1.2;   // 性能一般：适当增加延迟
    if (avgRenderTime < 500) return 1.4;   // 性能较差：显著增加延迟
    return 1.6;                            // 性能很差：大幅增加延迟
  }

  private calculateChangeFactor(currentMetrics: DocumentMetrics): number {
    if (!this.lastMetrics) return 1.0;

    // 如果文档结构变化很大，增加延迟确保稳定性
    const lengthChange = Math.abs(currentMetrics.length - this.lastMetrics.length) / this.lastMetrics.length;
    const complexityChange = Math.abs(currentMetrics.complexElementCount - this.lastMetrics.complexElementCount);

    if (lengthChange > 0.5 || complexityChange > 5) {
      return 1.3; // 大幅变化：增加延迟
    }
    
    if (lengthChange > 0.2 || complexityChange > 2) {
      return 1.1; // 中等变化：略微增加延迟
    }

    return 1.0; // 小幅变化：标准延迟
  }

  private recordPerformance(renderTime: number): void {
    this.performanceHistory.push(renderTime);
    
    // 保持历史记录在合理大小
    if (this.performanceHistory.length > this.maxHistorySize) {
      this.performanceHistory.shift();
    }
  }

  /**
   * 获取当前防抖延迟
   */
  getCurrentDelay(): number {
    return this.currentDelay;
  }

  /**
   * 获取文档分析结果
   */
  getLastMetrics(): DocumentMetrics | null {
    return this.lastMetrics;
  }

  /**
   * 获取性能历史
   */
  getPerformanceHistory(): number[] {
    return [...this.performanceHistory];
  }

  /**
   * 强制设置延迟（调试用）
   */
  forceDelay(delay: number): void {
    this.currentDelay = Math.max(this.config.minDelay, Math.min(this.config.maxDelay, delay));
  }

  /**
   * 重置所有状态
   */
  reset(): void {
    this.currentDelay = this.config.baseDelay;
    this.lastMetrics = null;
    this.performanceHistory = [];
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<DebounceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取建议的配置（基于当前使用情况）
   */
  getRecommendedConfig(): DebounceConfig {
    const avgPerformance = this.performanceHistory.length > 0 
      ? this.performanceHistory.reduce((a, b) => a + b, 0) / this.performanceHistory.length
      : 100;

    const config = { ...this.config };

    // 基于性能历史调整建议配置
    if (avgPerformance > 200) {
      config.baseDelay = Math.min(200, config.baseDelay * 1.2);
      config.maxDelay = Math.min(800, config.maxDelay * 1.1);
    } else if (avgPerformance < 80) {
      config.baseDelay = Math.max(80, config.baseDelay * 0.9);
      config.minDelay = Math.max(30, config.minDelay * 0.9);
    }

    return config;
  }

  /**
   * 调试信息
   */
  debug(): void {
    console.log('AdaptiveDebounceManager Debug Info:');
    console.log(`Current delay: ${this.currentDelay}ms`);
    console.log('Config:', this.config);
    console.log('Last metrics:', this.lastMetrics);
    console.log('Performance history:', this.performanceHistory);
    console.log('Recommended config:', this.getRecommendedConfig());
  }
}

/**
 * 创建防抖函数的工厂方法
 */
export function createAdaptiveDebounce<T extends (...args: any[]) => any>(
  fn: T,
  manager: AdaptiveDebounceManager
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    
    const delay = manager.getCurrentDelay();
    
    timeoutId = setTimeout(() => {
      const startTime = performance.now();
      
      fn(...args);
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // 记录性能用于下次调整
      if (args[0] && typeof args[0] === 'string') {
        manager.calculateDebounceDelay(args[0], renderTime);
      }
    }, delay);
  };
} 