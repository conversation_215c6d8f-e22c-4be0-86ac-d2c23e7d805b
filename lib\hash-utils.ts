/**
 * 快速哈希工具 - 性能优化专用
 * 使用FNV-1a 32位哈希算法，确保段落ID稳定性
 */

// FNV-1a 32位哈希算法实现
export function fastHash32(str: string): string {
  let hash = 0x811c9dc5; // FNV offset basis
  
  for (let i = 0; i < str.length; i++) {
    hash ^= str.charCodeAt(i);
    // FNV prime: 16777619 (0x01000193)
    hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);
  }
  
  // 确保结果为正数并转换为36进制字符串
  return (hash >>> 0).toString(36);
}

/**
 * 生成稳定的段落ID
 * 格式：{contentHash}:{startOffset}
 * 
 * @param content 段落纯文本内容
 * @param startOffset 段落在文档中的起始字符偏移量
 * @returns 稳定的段落ID
 */
export function generateStableSegmentId(content: string, startOffset: number): string {
  const contentHash = fastHash32(content.trim());
  return `${contentHash}:${startOffset}`;
}

/**
 * 解析段落ID，提取哈希和偏移量
 * 
 * @param segmentId 段落ID
 * @returns 解析结果 {hash: string, offset: number} 或 null
 */
export function parseSegmentId(segmentId: string): { hash: string; offset: number } | null {
  const parts = segmentId.split(':');
  if (parts.length !== 2) return null;
  
  const offset = parseInt(parts[1], 10);
  if (isNaN(offset)) return null;
  
  return {
    hash: parts[0],
    offset: offset
  };
}

/**
 * 验证段落ID格式是否正确
 * 
 * @param segmentId 段落ID
 * @returns 是否为有效格式
 */
export function isValidSegmentId(segmentId: string): boolean {
  return parseSegmentId(segmentId) !== null;
} 