import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';

// 导出类型
export type ExportFormat = 'html' | 'markdown' | 'pdf' | 'docx';
export type Platform = 'wechat' | 'xiaohongshu' | 'zhihu' | 'feishu' | 'general';

// 平台样式配置
const platformStyles = {
  wechat: {
    name: '微信公众号',
    css: `
      body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; line-height: 1.6; color: #333; }
      h1, h2, h3 { color: #2c3e50; margin-top: 1.5em; margin-bottom: 0.5em; }
      h1 { font-size: 1.8em; border-bottom: 2px solid #3498db; padding-bottom: 0.3em; }
      h2 { font-size: 1.5em; color: #e74c3c; }
      h3 { font-size: 1.3em; color: #f39c12; }
      p { margin: 1em 0; text-align: justify; }
      blockquote { border-left: 4px solid #3498db; margin: 1em 0; padding: 0.5em 1em; background: #f8f9fa; }
      code { background: #f1f2f6; padding: 0.2em 0.4em; border-radius: 3px; font-size: 0.9em; }
      pre { background: #2f3542; color: #f1f2f6; padding: 1em; border-radius: 5px; overflow-x: auto; }
      ul, ol { margin: 1em 0; padding-left: 2em; }
      li { margin: 0.5em 0; }
    `
  },
  xiaohongshu: {
    name: '小红书',
    css: `
      body { font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif; line-height: 1.7; color: #333; }
      h1, h2, h3 { color: #ff2442; margin-top: 1.5em; margin-bottom: 0.5em; font-weight: bold; }
      h1 { font-size: 1.8em; text-align: center; }
      h2 { font-size: 1.5em; }
      h3 { font-size: 1.3em; }
      p { margin: 1em 0; }
      blockquote { border-left: 4px solid #ff2442; margin: 1em 0; padding: 0.5em 1em; background: #fff5f5; }
      code { background: #ffe4e6; padding: 0.2em 0.4em; border-radius: 3px; color: #d63384; }
      pre { background: #f8f9fa; border: 1px solid #dee2e6; padding: 1em; border-radius: 5px; }
      ul, ol { margin: 1em 0; padding-left: 2em; }
      li { margin: 0.5em 0; }
      strong { color: #ff2442; }
    `
  },
  zhihu: {
    name: '知乎',
    css: `
      body { font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif; line-height: 1.6; color: #1a1a1a; }
      h1, h2, h3 { color: #1a1a1a; margin-top: 1.5em; margin-bottom: 0.5em; font-weight: 600; }
      h1 { font-size: 1.8em; }
      h2 { font-size: 1.5em; }
      h3 { font-size: 1.3em; }
      p { margin: 1em 0; }
      blockquote { border-left: 4px solid #0084ff; margin: 1em 0; padding: 0.5em 1em; background: #f6f8fa; }
      code { background: #f6f8fa; padding: 0.2em 0.4em; border-radius: 3px; font-family: "SF Mono", Monaco, Consolas, monospace; }
      pre { background: #f6f8fa; border: 1px solid #d0d7de; padding: 1em; border-radius: 6px; }
      ul, ol { margin: 1em 0; padding-left: 2em; }
      li { margin: 0.5em 0; }
    `
  },
  feishu: {
    name: '飞书',
    css: `
      body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Microsoft YaHei", sans-serif; line-height: 1.6; color: #262626; }
      h1, h2, h3 { color: #262626; margin-top: 1.5em; margin-bottom: 0.5em; font-weight: 600; }
      h1 { font-size: 1.8em; border-bottom: 1px solid #e8e8e8; padding-bottom: 0.3em; }
      h2 { font-size: 1.5em; }
      h3 { font-size: 1.3em; }
      p { margin: 1em 0; }
      blockquote { border-left: 4px solid #1890ff; margin: 1em 0; padding: 0.5em 1em; background: #f0f8ff; }
      code { background: #f5f5f5; padding: 0.2em 0.4em; border-radius: 3px; color: #d73a49; }
      pre { background: #f5f5f5; border: 1px solid #d9d9d9; padding: 1em; border-radius: 4px; }
      ul, ol { margin: 1em 0; padding-left: 2em; }
      li { margin: 0.5em 0; }
    `
  },
  general: {
    name: '通用',
    css: `
      body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 2em; }
      h1, h2, h3 { color: #2c3e50; margin-top: 1.5em; margin-bottom: 0.5em; }
      h1 { font-size: 2em; border-bottom: 2px solid #3498db; padding-bottom: 0.3em; }
      h2 { font-size: 1.6em; }
      h3 { font-size: 1.3em; }
      p { margin: 1em 0; }
      blockquote { border-left: 4px solid #3498db; margin: 1em 0; padding: 0.5em 1em; background: #f8f9fa; }
      code { background: #f1f2f6; padding: 0.2em 0.4em; border-radius: 3px; font-size: 0.9em; }
      pre { background: #2f3542; color: #f1f2f6; padding: 1em; border-radius: 5px; overflow-x: auto; }
      ul, ol { margin: 1em 0; padding-left: 2em; }
      li { margin: 0.5em 0; }
      table { border-collapse: collapse; width: 100%; margin: 1em 0; }
      th, td { border: 1px solid #ddd; padding: 0.5em; text-align: left; }
      th { background: #f8f9fa; font-weight: bold; }
    `
  }
};

// 导出工具类
export class ExportUtils {
  // 将Markdown转换为HTML
  static async markdownToHtml(markdown: string): Promise<string> {
    try {
      const processor = unified()
        .use(remarkParse)
        .use(remarkGfm)
        .use(remarkRehype, { allowDangerousHtml: true })
        .use(rehypeRaw)
        .use(rehypeHighlight)
        .use(rehypeStringify);

      const result = await processor.process(markdown);
      return String(result);
    } catch (error) {
      console.error('Markdown to HTML conversion error:', error);
      throw new Error('转换失败');
    }
  }

  // 生成带样式的完整HTML文档
  static generateStyledHtml(content: string, platform: Platform = 'general'): string {
    const platformConfig = platformStyles[platform];
    
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown文档 - ${platformConfig.name}</title>
    <style>
        ${platformConfig.css}
    </style>
</head>
<body>
    ${content}
</body>
</html>`;
  }

  // 下载文件
  static downloadFile(content: string, filename: string, mimeType: string = 'text/plain') {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  // 导出为HTML
  static async exportAsHtml(markdown: string, platform: Platform = 'general') {
    try {
      const htmlContent = await this.markdownToHtml(markdown);
      const styledHtml = this.generateStyledHtml(htmlContent, platform);
      const filename = `markdown-export-${platform}-${new Date().toISOString().slice(0, 10)}.html`;
      this.downloadFile(styledHtml, filename, 'text/html');
    } catch (error) {
      console.error('Export as HTML failed:', error);
      throw error;
    }
  }

  // 导出为Markdown
  static exportAsMarkdown(markdown: string) {
    const filename = `markdown-export-${new Date().toISOString().slice(0, 10)}.md`;
    this.downloadFile(markdown, filename, 'text/markdown');
  }

  // 复制HTML到剪贴板
  static async copyHtmlToClipboard(markdown: string, platform: Platform = 'general') {
    try {
      const htmlContent = await this.markdownToHtml(markdown);
      await navigator.clipboard.writeText(htmlContent);
      return true;
    } catch (error) {
      console.error('Copy to clipboard failed:', error);
      return false;
    }
  }

  // 获取平台列表
  static getPlatforms() {
    return Object.entries(platformStyles).map(([key, config]) => ({
      id: key as Platform,
      name: config.name
    }));
  }
}
