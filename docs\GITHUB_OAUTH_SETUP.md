# GitHub OAuth应用设置指南

为了使用GitHub第三方登录功能，您需要在GitHub上创建一个OAuth应用。

## 步骤1: 创建GitHub OAuth应用

1. 登录您的GitHub账户
2. 访问 [GitHub Developer Settings](https://github.com/settings/developers)
3. 点击 "OAuth Apps" 标签
4. 点击 "New OAuth App" 按钮

## 步骤2: 填写应用信息

在创建OAuth应用的表单中，填写以下信息：

- **Application name**: `Markdown Editor` (或您喜欢的名称)
- **Homepage URL**: `http://localhost:3000`
- **Application description**: `一个支持Git工作流的Markdown编辑器`
- **Authorization callback URL**: `http://localhost:3000/api/auth/callback/github`

## 步骤3: 获取客户端凭据

创建应用后，您将看到：
- **Client ID**: 复制这个值
- **Client Secret**: 点击 "Generate a new client secret" 生成并复制

## 步骤4: 配置环境变量

在项目根目录的 `.env.local` 文件中设置以下变量：

```env
# NextAuth配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here-please-change-this-in-production

# GitHub OAuth应用配置
GITHUB_CLIENT_ID=your-github-client-id-here
GITHUB_CLIENT_SECRET=your-github-client-secret-here
```

## 步骤5: 重启开发服务器

保存 `.env.local` 文件后，重启开发服务器：

```bash
npm run dev
```

## 功能说明

配置完成后，您将能够：

1. **GitHub登录**: 点击顶栏的"GitHub登录"按钮
2. **访问仓库**: 登录后可以选择您的GitHub仓库
3. **实时同步**: 直接编辑GitHub仓库中的Markdown文件
4. **权限管理**: 基于GitHub的访问权限控制

## 权限范围

OAuth应用请求以下权限：
- `read:user`: 读取用户基本信息
- `user:email`: 读取用户邮箱地址
- `repo`: 访问公开和私有仓库

## 安全注意事项

1. **保护密钥**: 永远不要将Client Secret提交到版本控制
2. **生产环境**: 在生产环境中使用强密码作为NEXTAUTH_SECRET
3. **HTTPS**: 生产环境必须使用HTTPS
4. **回调URL**: 确保回调URL与实际部署地址匹配

## 故障排除

### 常见问题

1. **"Invalid client"错误**
   - 检查Client ID和Client Secret是否正确
   - 确认回调URL是否匹配

2. **"Redirect URI mismatch"错误**
   - 检查OAuth应用中的回调URL设置
   - 确保URL完全匹配（包括协议和端口）

3. **环境变量未生效**
   - 重启开发服务器
   - 检查.env.local文件格式

### 调试技巧

1. 检查浏览器开发者工具的网络标签
2. 查看服务器控制台的错误信息
3. 确认GitHub OAuth应用状态为"Active"

## 生产部署

部署到生产环境时，需要：

1. 创建新的OAuth应用（或更新现有应用）
2. 设置正确的生产域名和回调URL
3. 配置生产环境的环境变量
4. 使用HTTPS协议

例如，如果部署到 `https://your-domain.com`：
- Homepage URL: `https://your-domain.com`
- Authorization callback URL: `https://your-domain.com/api/auth/callback/github`
