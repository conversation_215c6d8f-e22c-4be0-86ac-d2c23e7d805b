# 专家复核：实时模式卡顿的**根因 + 纠偏方案 + 需求清单**

> **结论**：两位技术员都准确抓住了瓶颈（全量分割、同步渲染、DOM 暴涨、事件 O(n²) 搜索等），但**当前修复计划仍停留在“分析”与“局部优化”**；要想在 500-1000 段、10 k+ 行文档下保持 < 150 ms 响应，必须**把实时模式迁到与 M2 同级的“块-Worker-虚拟化”架构**。  

下面按 **问题确认 → P0 快修（7 天） → P1 架构对齐（4 周） → 立即需要的资料** 四段说明。

---

## 1 ⃣ 问题确认（文件证据）

| 现象 | 文件证据 |
|------|----------|
| **① 无任何内部优化库**，仍用同步 unified → rehype | “内部依赖：无”|
| **② 每次 content 改变全量 `split` + `renderMarkdown`** | `paragraphs = useMemo(... renderMarkdown())`|
| **③ `renderMarkdown()` 同步 + 每次新建 processor** | `processor.processSync`|
| **④ 编辑一段触发重建全文** | `finishEditing()` 拼接全部段落|
| **⑤ 事件监听每改一次 content 就重绑** + 行号 O(n²) 搜索 | `addEventListener` 依赖 content|
| **⑥ 性能监控 / 防抖 / 增量解析全未接入** | 对比表全部 ❌|

---

## 2 ⃣ P0 — **一周内可落地的快修**（目标：响应 P95 ≤ 300 ms）

| 优先级 | 动作 | 要点 | 预期收益 |
|--------|------|------|----------|
| 🔴 | **接入 AdaptiveDebounce** | `onChange → debounceManager.getDelay()`；初始区间 80-300 ms | 减少渲染触发频次 |
| 🔴 | **Markdown 渲染搬到 Worker** | 复用 M2 render Worker；主线程改为 `await worker.renderBatch(changedParagraphs)` | 主线程长任务≈0 |
| 🔴 | **段落级缓存 Map<markdown,html>** | 先用 `WeakMap` + hash；命中率>80 % | 渲染时间从 500 ms→<100 ms |
| 🟠 | `ParagraphBlock` `React.memo` + key 改用 `paragraph.hash` | 仅重渲染脏段 | DOM diff < 50 ms |
| 🟠 | 事件监听拆到 `useEffect([])`，用 `ref.current` 持 content | 不再频繁重绑 | 减少内存 & CPU 抖动 |
| 🟡 | 性能监控最小接入 (`PerformanceMonitor.markStart/End`) | 记录 “debounce wait / Worker 渲染 / DOM commit” | 后续调优有数据 |

---

## 3 ⃣ P1 — **4 周完成的结构重构**（与 M2 同级）

1. **Block Tree + 增量解析**  
   * 将 `IncrementalParser` 改为返回 `{added, updated, removed}` 块数组；`LiveRenderMode` 只 diff 这些块。  
   * 段落 ID = `hash32(markdown) + ':' + startOffset`（与预览/分屏统一）。

2. **双层虚拟化**  
   * 视觉层：`react-window` / `@tanstack/react-virtual`，视口 ±2 屏块进 DOM。  
   * 协作层：只对“可见 ±M 屏”块推增量渲染，远端 ops 延迟合并。

3. **Worker Pool**  
   * Markdown → HTML、标题 ID、代码高亮、TOC 统统在 Worker；主线程只拿字符串。  
   * 加 `rev` 号：慢包丢弃，不会覆盖快包。

4. **统一监控**  
   * 长任务、FPS、heap；CI 基准：1000 段文档 Total < 200 ms，否则构建失败。

---

## 4 ⃣ 需要技术员**立刻补充**的资料

| # | 说明 | 用途 |
|---|------|------|
| 1 | **增量解析 API**：`parseIncremental()` 当前返回结构 & 受影响行定位算法 | 确认能否复用 |
| 2 | **Worker 构建方式**：现用哪种打包？能否支持多线程 pool？ | 评估渲染池实现 |
| 3 | **计划中的虚拟列表库**（自研 / react-window / virtuoso） | 提前给出高度计算策略 |
| 4 | **协作 OT / CRDT 方案**（如果未来要多人实时） | 决定 Block key 与 merge 规则 |

> 收到以上信息后，我将给出 **逐文件改动示例（diff patch）+ Worker 渲染骨架 + 虚拟列表封装**。

---

### ★ 技术组件引用表

| 组件 | 实时模式现状 | 目标 |
|------|-------------|------|
| IncrementalParser | 未接入 | P0：forceReparse → P1：parseIncremental |
| AdaptiveDebounce  | 未接入 | P0：直接调用 |
| PerformanceMonitor| 未接入 | P0：最小埋点；P1：全指标 |
| Worker 渲染管线   | 无    | P0：单 Worker；P1：WorkerPool |
| 虚拟滚动          | 无    | P1：react-window |

---

**请技术员按上表提供缺口信息，并确认 P0 排期（7 天）。**  
若有任何实现细节阻塞，例如 Worker 构建、缓存 hash 策略、虚拟列表高度计算等，随时把代码段或报错贴给我，我会给出针对性的修补示例。
