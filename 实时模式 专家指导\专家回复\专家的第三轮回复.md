````markdown
# 专家第三轮答复 —— P0 关键实现细节 & 疑问逐条澄清  
（直接复制给两位技术员即可）

---

## 0. 依赖版本总览

| 包 | 建议版本 | 说明 |
|----|----------|------|
| `worker-loader` | `^3.0.8` | 最新版同时支持 Webpack 5 / Next 13 |
| `diff`          | `^5.2.0` | 轻量无依赖；含 TypeScript 定义 |
| `@tanstack/react-virtual` | `^3.0.0` | 核心 API 已稳定 |
| LRU 实现        | 推荐 `lru-cache@^10`（3 kB）或自行实现 ≤ 50 行 |

---

## 1. aug 组问题澄清

### 1.1 `useAdaptiveDebounce` Hook

```ts
// lib/useAdaptiveDebounce.ts
import { useRef, useEffect } from 'react';
import { AdaptiveDebounceManager } from '@/lib/adaptive-debounce';

export function useAdaptiveDebounce<T>(
  value: T,
  callback: (v: T) => void,
  mgr = new AdaptiveDebounceManager()
) {
  const timer = useRef<ReturnType<typeof setTimeout>>();
  const lastRender = useRef<number>(Date.now());

  useEffect(() => {
    const delay = mgr.calculateDebounceDelay(String(value), lastRender.current);
    clearTimeout(timer.current);
    timer.current = setTimeout(() => {
      lastRender.current = Date.now();
      callback(value);
    }, delay);
    return () => clearTimeout(timer.current);
  }, [value]);
}
````

> 可 **复用现有 `AdaptiveDebounceManager`**，无需改它。仅新增此 Hook。

---

### 1.2 `diffLines` & 超长文档阈值

```ts
const MAX_DIFF_LINES = 10_000;   // >1.5 MB 典型文件
const lineCount = newTxt.split('\n').length;
if (lineCount > MAX_DIFF_LINES) return forceReparse(newTxt);
const diff = diffLines(prevTxt, newTxt, { newlineIsToken: true });
```

*阈值理由*：`diff` 复杂度近似 *O(n · d)*；超 10 k 行风险大，直接重解析仍 <150 ms（Worker）。

---

### 1.3 WorkerPool 骨架

```ts
// lib/worker-pool.ts
export class WorkerPool {
  private pool: Worker[] = [];
  private queue: (() => void)[] = [];
  constructor(size = Math.max(1, (navigator.hardwareConcurrency || 4) >> 1)) {
    for (let i = 0; i < size; i++) this.pool.push(this.spawn());
  }
  private spawn() {
    const w = new Worker(new URL('@/worker/markdown-render.worker.ts', import.meta.url));
    w.onerror = () => { console.warn('Worker crashed, respawn'); this.pool.splice(this.pool.indexOf(w),1); this.pool.push(this.spawn()); };
    return w;
  }
  async renderMarkdown(batch: Block[], rev: number) {
    return new Promise<RenderResult[]>((res, rej) => {
      const work = () => {
        const w = this.pool.pop();
        if (!w) { this.queue.push(work); return; }           // no idle
        w.onmessage = ({data}) => { this.pool.push(w!); res(data.results); if (this.queue.length) this.queue.shift()!(); };
        w.postMessage({ type:'render', batch, rev });
      };
      work();
    });
  }
  destroy() { this.pool.forEach(w => w.terminate()); this.pool.length = 0; }
}
```

> 负载均衡：最简单的 **“空闲即取”**；失败自动 respawn。足够满足 P0。

---

### 1.4 `applyBlockChanges`

```ts
export function applyBlockChanges(prev: Block[], diff: RenderResult[]): Block[] {
  const map = new Map(prev.map(b => [b.id, b]));
  diff.forEach(r => {
    if (r.type === 'remove') map.delete(r.id);
    else map.set(r.id, { ...map.get(r.id), ...r });      // add / update
  });
  return Array.from(map.values()).sort((a,b)=>a.startOffset-b.startOffset);
}
```

*React key*：始终用 `block.id`（哈希+offset），只要 `id` 不变，DOM 节点可复用。

---

### 1.5 PerformanceMonitor 用法

```ts
import { performanceMonitor as perf } from '@/lib/performance-monitor';

perf.markStart('worker');  // 开始
// ...
perf.markEnd('worker');    // 结束并自动上报
```

*生产关停*：在 `PerformanceMonitor` 内部 `if (process.env.NODE_ENV !== 'production')` 判断即可，不用组件再判断。

---

### 1.6 TypeScript + worker-loader

```ts
// 声明.d.ts
declare module '*.worker.ts' {
  class WebpackWorker extends Worker {
    constructor();
  }
  export default WebpackWorker;
}
// 使用
import MarkdownWorker from '@/worker/markdown-render.worker.ts';
const worker = new MarkdownWorker();
```

---

### 1.7 虚拟滚动启用 & FPS 监控

*DOM 节点*：指 **编辑器容器内元素**。
*FPS*：用 `requestAnimationFrame` 采样 1 s；若平均 <50 则 `setUseVirtual(true)`；启用时先保持滚动位置 `scrollTop`，再切虚拟列表，因块高度恒定不会跳。

---

## 2. cursor 组问题澄清

| 疑问                | 答复                                                                                             |
| ----------------- | ---------------------------------------------------------------------------------------------- |
| **依赖版本**          | 见 0.                                                                                           |
| **Block 接口**      | 加 `html?:string`、`dirty?:boolean`，其余 OK。                                                       |
| **Worker 协议**     | `error` 类型：`{type:'error', rev, message}`。Worker 端 `try…catch` 把异常回主线程；主线程记录 + fallback 主线程渲染。 |
| **LRU**           | 用 `lru-cache@10`，`max=2000` ≈ 2000 段 ≈ 3 MB HTML；淘汰策略默认 LRU 即可。                                |
| **监控指标**          | 建议指标键：`parse`,`worker`,`dom`；现有类可直接用。                                                          |
| **错误处理**          | 若 Worker 超 2 s 未回包 → `perf.markTag('worker-timeout')` → fallback 主线程 + 重启 Worker。              |
| **Playwright 脚本** | 见 §3。                                                                                          |
| **调试**            | Chrome DevTools > Performance → WebWorker；在 `Worker` 情况下仍能取 JS profile。                        |

---

## 3. Playwright 性能脚本精简示例

```ts
import { test, expect } from '@playwright/test';

test('live mode perf', async ({ page }) => {
  await page.goto('/editor/live?fixture=long.md');
  // 输入 100 字符
  await page.locator('.cm-content').type('a'.repeat(100));
  // 滚动
  await page.locator('#editor-scroll').evaluate(el => el.scrollTo(0, 2000));
  // 导出 trace
  await page.tracing.start({ screenshots: false, snapshots: false });
  await page.waitForTimeout(5000);
  await page.tracing.stop({ path: 'trace.zip' });
  // 指标校验
  const metrics = await page.metrics();
  expect(metrics.TaskDuration).toBeLessThan(0.05);        // 50 ms
});
```

---

## 4. 接下来要做什么

1. **按本答复中代码落地 hook / pool / applyBlockChanges / d.ts**。
2. **Day 3 末** 之前——提交可跑的基础分支；我将针对代码做 *inline review*。
3. **Day 5**——上传 Playwright trace + Lighthouse JSON；我提供最终调优意见。

如在实现中遇到任何 TS 报错、Webpack 构建或性能瓶颈，直接贴 stacktrace 或 flamegraph，我做针对性 patch。

加油💪 —— 距离 P0 结束还剩 **≤ 7 天**！

```
::contentReference[oaicite:4]{index=4}
```
