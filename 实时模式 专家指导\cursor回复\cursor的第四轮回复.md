# Cursor 对专家第四轮指导的最终确认

## 📋 专家指导完全确认

感谢专家提供的最终技术方案！所有技术细节已完全明确，我们准备立即开始开发：

### ✅ 所有技术细节已确认

1. **Block/RenderResult接口** ✅ 完全理解
   - Block：`id`, `content`, `hash`, `startOffset`, `endOffset`, `html?`, `dirty?`, `type?`
   - RenderResult：`id`, `html?`, `type`, `hash?`

2. **Worker文件组织** ✅ 完全理解
   - 源码目录：`src/worker/markdown-render.worker.ts`
   - 导入路径：`@/worker/markdown-render.worker.ts`
   - tsconfig配置已确认

3. **Worker错误处理** ✅ 完全理解
   - 2秒超时机制：`Promise.race` + fallback
   - 自动respawn：`pool.onError` + `terminate()`
   - 性能标记：`perf.markTag('worker-timeout')`

4. **LiveRenderMode改造** ✅ 完全理解
   - 使用 `useIncrementalRender` Hook
   - 实现 `patchBlock` 函数
   - 删除 `finishEditing` 逻辑

5. **性能监控策略** ✅ 完全理解
   - development：console + Performance API
   - production：收集指标但不打印，通过 `window.__PERF__` 暴露

6. **虚拟列表启用条件** ✅ 完全理解
   - blocks.length > 1500
   - domNodeCount > 2500
   - measuredFPS < 50

---

## 🚀 开发准备就绪

### 技术栈确认
```bash
# 依赖包版本
worker-loader@^3.0.8
diff@^5.2.0
@tanstack/react-virtual@^3.0.0
lru-cache@^10
@types/diff
```

### 文件结构确认
```
src/
├── lib/
│   ├── useAdaptiveDebounce.ts
│   ├── worker-pool.ts
│   ├── applyBlockChanges.ts
│   └── patchBlock.ts
├── worker/
│   └── markdown-render.worker.ts
├── types/
│   └── worker.d.ts
└── components/editor/
    └── LiveRenderMode.tsx (改造)
```

### 实施计划确认
- **Day 1-2**：基础架构搭建
- **Day 3**：提交基础分支，等待专家inline review
- **Day 5**：提交Playwright trace + Lighthouse JSON
- **Day 7**：P0完成验收

---

## 📋 最终技术确认

### 1. **Block接口** ✅ 确认
```typescript
export interface Block {
  id: string;           // `${hash32(content.trim())}:${startOffset}`
  content: string;      // 段落原始Markdown文本
  hash: string;         // 32-bit hash of content.trim()
  startOffset: number;  // 段内绝对偏移
  endOffset: number;    // 段内绝对偏移
  html?: string;        // Worker渲染后的HTML
  dirty?: boolean;      // 本次编辑是否受影响
  type?: 'paragraph' | 'heading' | 'listItem' | 'codeFence' | 'html';
}
```

### 2. **RenderResult接口** ✅ 确认
```typescript
export interface RenderResult {
  id: string;           // = block.id
  html?: string;        // add/update时必须；remove可省
  type: 'add' | 'update' | 'remove';
  hash?: string;        // Worker侧可返回新hash
}
```

### 3. **Worker错误处理** ✅ 确认
```typescript
const TIMEOUT_MS = 2000;

function renderWithTimeout(batch: Block[], rev: number, pool: WorkerPool) {
  return Promise.race([
    pool.renderMarkdown(batch, rev),
    new Promise<never>((_, rej) =>
      setTimeout(() => rej(new Error('timeout')), TIMEOUT_MS)
    ),
  ]).catch(err => {
    perf.markTag('worker-timeout');
    return renderMarkdownOnMainThread(batch);
  });
}
```

### 4. **虚拟列表启用条件** ✅ 确认
```typescript
const SHOULD_VIRTUAL =
  blocks.length > 1500 ||         // 超1500段
  domNodeCount > 2500 ||          // DOM > 2500
  measuredFPS < 50;               // FPS < 50
```

---

## 🎯 开发目标确认

### 性能目标
- P95输入延迟 < 300ms
- 长任务 < 50ms
- FPS > 55
- DOM nodes < 2500
- Heap增长 < 10%

### 功能目标
- 增量渲染：只渲染变化的段落
- Worker渲染：异步处理Markdown
- 虚拟列表：大文档性能优化
- 错误处理：Worker失败时fallback

---

## 📋 专家时间线确认

| Day | 交付内容 | 专家动作 |
|-----|----------|----------|
| **3** | 基础分支 + Worker构建OK | inline review / patch建议 |
| **5** | Playwright trace + Lighthouse | 指标复核 |
| **7** | P0性能门槛达标 | 出具✅完成确认 |

---

## 🔧 开发准备清单

### ✅ 已完成
- [x] 技术方案完全理解
- [x] 接口定义确认
- [x] 错误处理策略确认
- [x] 性能监控策略确认
- [x] 虚拟列表策略确认
- [x] 实施计划确认

### 🚀 准备开始
- [ ] 安装依赖包
- [ ] 创建文件结构
- [ ] 实现核心组件
- [ ] 集成测试
- [ ] 性能优化

---

**所有技术细节已完全确认，可以开始开发！**

**预计Day 3提交基础分支供专家review，Day 7完成P0快修。** 