# GitHub 历史提交查看功能

## 功能概述

新增的历史提交查看功能允许用户在左侧栏中查看GitHub仓库的提交历史，并能够查看特定提交时的文件内容。

## 功能特性

### 🎛️ 控制面板
- **🔗 仓库连接**: 快速连接GitHub仓库（GitBranch按钮）
- **🔄 刷新功能**: 重新加载最新的提交历史（RefreshCw按钮）

### 🕒 提交历史浏览
- **完整提交列表**: 显示仓库的所有提交记录
- **分页加载**: 支持加载更多历史提交
- **搜索功能**: 可以按提交信息或作者搜索
- **实时信息**: 显示提交时间、作者、SHA等详细信息

### 📊 提交详情展示
- **提交统计**: 显示添加/删除的行数和修改的文件数量
- **文件列表**: 展示每个提交中修改的文件
- **作者头像**: 显示GitHub用户头像
- **相对时间**: 智能显示相对时间（今天、昨天、X天前等）

### 📄 历史文件查看
- **文件内容预览**: 查看文件在特定提交时的内容
- **全屏查看**: 支持模态框全屏查看文件内容
- **文件恢复**: 可以将文件恢复到历史版本
- **语法高亮**: 支持代码文件的语法高亮显示

### 🎨 用户体验
- **统一界面风格**: 与源代码控制面板保持一致的设计风格
- **三个核心按钮**: 仓库连接、刷新、重置功能，与Git面板完全一致
- **GitHub风格设计**: 参考GitHub提交历史的布局和样式
- **清晰的信息层次**: 头像、提交消息、作者信息、时间和SHA的合理布局
- **用户引导**: 未登录或未连接仓库时显示友好的引导界面
- **一键连接**: 提供快捷按钮直接在历史面板中连接仓库
- **智能提示**: 详细说明功能特性和使用步骤
- **保持上下文**: 连接仓库后自动返回历史面板，无需切换标签页
- **响应式交互**: 悬停效果、选中状态、加载动画等细节优化

## 使用方法

### 1. 访问历史面板
1. 点击左侧栏的"历史"图标（时钟图标）
2. 如果未登录GitHub，会显示登录提示和功能说明
3. 如果未连接仓库，会显示"选择GitHub仓库"按钮
4. 点击按钮可以直接在历史面板中选择并连接仓库
5. 连接完成后会自动加载提交历史，无需切换面板

### 2. 使用控制按钮
**🔗 仓库连接按钮（GitBranch图标）**：
- 打开GitHub仓库选择器
- 可以选择任意有权限访问的GitHub仓库
- 选择后自动切换到该仓库并加载提交历史
- 支持搜索和分页浏览仓库列表

**🔄 刷新按钮（RefreshCw图标）**：
- 重新从GitHub API获取最新的提交历史
- 在按钮加载时会显示旋转动画
- 用于同步远程仓库的最新更改

### 2. 浏览提交历史
- **查看提交**: 提交列表按时间倒序排列
- **搜索提交**: 在搜索框中输入关键词搜索
- **加载更多**: 滚动到底部点击"加载更多"

### 3. 查看提交详情
1. 点击任意提交记录
2. 右侧面板将显示提交详细信息
3. 查看修改的文件列表和统计信息

### 4. 查看历史文件
1. 在历史面板中选择一个提交
2. 中央区域将显示提交详情和修改的文件列表
3. 点击文件列表中的任意文件查看其内容
4. 点击"恢复此版本"按钮恢复文件到该版本
5. 点击"在编辑器中打开"按钮在编辑器中打开文件

## 界面布局

新的布局采用了更符合用户习惯的设计，参考GitHub的提交历史界面：

```
┌─────────────┬─────────────────────────────────────────────┐
│ 左侧栏       │ 中央区域                                     │
│             │                                             │
│ 📋 历史面板  │ 📄 提交详情视图                              │
│ ┌─────────┐ │                                             │
│ │🔍搜索框 │ │ ┌─────────────────────────────────────────┐ │
│ └─────────┘ │ │ 提交信息                                 │ │
│             │ │ 作者 | 时间 | SHA | 统计                │ │
│ 👤 提交1     │ └─────────────────────────────────────────┘ │
│ 📝 消息      │                                             │
│ 👤 作者 时间  │ ┌─────────────┬─────────────────────────────┐ │
│ [abc123]    │ │ 修改的文件   │ 文件内容预览                 │ │
│ ─────────── │ │             │                             │ │
│ 👤 提交2     │ │ • file1.md  │ # 文件内容                   │ │
│ 📝 消息      │ │ • file2.md  │ 这里显示选中文件的内容...     │ │
│ 👤 作者 时间  │ │             │                             │ │
│ [def456]    │ │             │ [恢复此版本] [在编辑器中打开] │ │
│ ─────────── │ └─────────────┴─────────────────────────────┘ │
│ [加载更多]   │                                             │
└─────────────┴─────────────────────────────────────────────┘
```

### 🎨 设计特色

**GitHub风格的提交列表**：
- 每个提交占用一行，用分割线分隔
- 左侧显示作者头像
- 提交消息作为主要信息突出显示
- 作者、时间信息以较小字体显示在下方
- SHA值以标签形式显示在右侧
- 悬停和选中状态有明显的视觉反馈

**优化的信息布局**：
- 仓库信息以标签形式显示在标题旁
- 搜索框有更好的视觉层次
- 加载状态和空状态有专门的设计
- 按钮和交互元素更加精致

## 技术实现

### API 集成
- 使用GitHub REST API v3
- 支持分页和搜索
- 获取提交详情和文件内容

### 组件结构
```
HistoryPanel.tsx (左侧栏)
├── 搜索框
├── 提交列表
│   ├── 提交项目
│   └── 分页加载
└── 提交选择处理

CommitDetailView.tsx (中央区域)
├── 提交信息头部
├── 统计数据显示
├── 文件列表 (左侧)
└── 文件内容预览 (右侧)
    ├── 内容显示
    └── 操作按钮
```

### 数据流
1. 从GitHub API获取提交列表 (HistoryPanel)
2. 选择提交时获取详细信息并传递到中央区域
3. 在CommitDetailView中显示提交详情和文件列表
4. 点击文件时获取历史内容并显示
5. 支持文件恢复到本地Git服务和编辑器打开

## 📁 新增文件
- `components/git/HistoryPanel.tsx` - 历史面板主组件（左侧栏）
- `components/git/CommitDetailView.tsx` - 提交详情视图组件（中央区域）
- `docs/HISTORY_FEATURE.md` - 功能说明文档

## 🔄 修改文件
- `components/layout/LeftSidebar.tsx` - 添加历史标签页和回调处理
- `lib/github.ts` - 增强GitHub API，添加提交相关接口
- `app/page.tsx` - 添加提交内容显示逻辑和CommitDetailView集成

## 测试场景

### 场景1：未连接仓库
1. 用户登录GitHub账户
2. 点击左侧栏的"历史"图标
3. 显示"请先连接GitHub仓库"提示和"选择GitHub仓库"按钮
4. 点击按钮，弹出仓库选择对话框
5. 选择仓库后，自动连接并加载提交历史
6. 用户可以直接在历史面板中浏览提交历史

### 场景2：已连接仓库
1. 用户登录GitHub账户并已连接仓库
2. 点击左侧栏的"历史"图标
3. 直接显示提交历史列表
4. 点击任意提交，中央区域显示详情
5. 点击文件列表中的文件，显示历史版本内容

## 注意事项

- 需要GitHub登录才能使用此功能
- 需要连接到GitHub仓库
- 大文件可能加载较慢
- 恢复文件会覆盖当前内容，建议先保存

## 未来改进

- [ ] 支持分支切换查看不同分支的历史
- [ ] 添加文件差异对比功能
- [ ] 支持提交图形化展示
- [ ] 添加提交标签和里程碑显示
- [ ] 支持批量文件操作
