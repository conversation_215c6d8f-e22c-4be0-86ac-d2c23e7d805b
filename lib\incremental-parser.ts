import { ChangeDesc as CodeMirrorChangeDesc } from '@codemirror/state';
import { BoundaryIndex, ChangeDesc } from './boundary-index';
import { generateStableSegmentId } from './hash-utils';

export interface SegmentChange {
  type: 'add' | 'remove' | 'update' | 'unchanged';
  segmentId: string;
  content: string;
  startOffset: number;
  endOffset: number;
}

export interface IncrementalParseResult {
  changes: SegmentChange[];
  affectedRange: { start: number; end: number };
  newBoundaryIndex: BoundaryIndex;
}

export class IncrementalParser {
  private boundaryIndex: BoundaryIndex;
  private lastContent: string;
  private lastSegments: Map<string, { content: string; startOffset: number; endOffset: number }>;

  constructor(content: string) {
    this.lastContent = content;
    this.boundaryIndex = new BoundaryIndex(content);
    this.lastSegments = new Map();
    
    // 初始化时建立段落映射
    this.initializeSegments(content);
  }

  private initializeSegments(content: string): void {
    const segments = this.splitIntoSegments(content);
    this.lastSegments.clear();
    
    segments.forEach(segment => {
      this.lastSegments.set(segment.segmentId, {
        content: segment.content,
        startOffset: segment.startOffset,
        endOffset: segment.endOffset
      });
    });
  }

  private splitIntoSegments(content: string): Array<{
    segmentId: string;
    content: string;
    startOffset: number;
    endOffset: number;
  }> {
    const segments: Array<{
      segmentId: string;
      content: string;
      startOffset: number;
      endOffset: number;
    }> = [];

    // 按段落分割（空行分隔）
    const lines = content.split('\n');
    let currentSegment: string[] = [];
    let currentStartOffset = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      if (line.trim() === '' && currentSegment.length > 0) {
        // 遇到空行，结束当前段落
        const segmentContent = currentSegment.join('\n');
        const segmentId = generateStableSegmentId(segmentContent, currentStartOffset);
        const endOffset = currentStartOffset + segmentContent.length;
        
        segments.push({
          segmentId,
          content: segmentContent,
          startOffset: currentStartOffset,
          endOffset
        });
        
        currentSegment = [];
        currentStartOffset = endOffset + 1; // +1 for the newline
      } else if (line.trim() !== '') {
        // 非空行，添加到当前段落
        if (currentSegment.length === 0) {
          currentStartOffset = this.boundaryIndex.getLineStart(i);
        }
        currentSegment.push(line);
      } else {
        // 空行，更新偏移量
        if (currentSegment.length === 0) {
          currentStartOffset = this.boundaryIndex.getLineStart(i + 1);
        }
      }
    }

    // 处理最后一个段落
    if (currentSegment.length > 0) {
      const segmentContent = currentSegment.join('\n');
      const segmentId = generateStableSegmentId(segmentContent, currentStartOffset);
      const endOffset = currentStartOffset + segmentContent.length;
      
      segments.push({
        segmentId,
        content: segmentContent,
        startOffset: currentStartOffset,
        endOffset
      });
    }

    return segments;
  }

  /**
   * 基于CodeMirror变更进行增量解析
   */
  parseIncremental(newContent: string, changes: CodeMirrorChangeDesc): IncrementalParseResult {
    // 更新边界索引
    const newBoundaryIndex = new BoundaryIndex(newContent);
    // 注意：M1阶段简化实现，M2阶段会实现真正的增量更新
    // newBoundaryIndex.applyChanges(changes, newContent);

    // 计算受影响的范围  
    const affectedRange = this.calculateAffectedRange(changes);
    
    // 获取新的段落列表
    const newSegments = this.splitIntoSegments(newContent);
    const newSegmentMap = new Map(
      newSegments.map(seg => [seg.segmentId, seg])
    );

    // 生成变更列表
    const segmentChanges: SegmentChange[] = [];
    const processedIds = new Set<string>();

    // 检查现有段落的变化
    for (const [oldId, oldSegment] of Array.from(this.lastSegments)) {
      const newSegment = newSegmentMap.get(oldId);
      
      if (!newSegment) {
        // 段落被删除
        segmentChanges.push({
          type: 'remove',
          segmentId: oldId,
          content: oldSegment.content,
          startOffset: oldSegment.startOffset,
          endOffset: oldSegment.endOffset
        });
      } else if (newSegment.content !== oldSegment.content) {
        // 段落内容变化
        segmentChanges.push({
          type: 'update',
          segmentId: oldId,
          content: newSegment.content,
          startOffset: newSegment.startOffset,
          endOffset: newSegment.endOffset
        });
      } else {
        // 段落无变化
        segmentChanges.push({
          type: 'unchanged',
          segmentId: oldId,
          content: newSegment.content,
          startOffset: newSegment.startOffset,
          endOffset: newSegment.endOffset
        });
      }
      
      processedIds.add(oldId);
    }

    // 检查新增的段落
    for (const newSegment of newSegments) {
      if (!processedIds.has(newSegment.segmentId)) {
        segmentChanges.push({
          type: 'add',
          segmentId: newSegment.segmentId,
          content: newSegment.content,
          startOffset: newSegment.startOffset,
          endOffset: newSegment.endOffset
        });
      }
    }

    // 更新状态
    this.lastContent = newContent;
    this.boundaryIndex = newBoundaryIndex;
    this.lastSegments.clear();
    newSegments.forEach(segment => {
      this.lastSegments.set(segment.segmentId, {
        content: segment.content,
        startOffset: segment.startOffset,
        endOffset: segment.endOffset
      });
    });

    return {
      changes: segmentChanges,
      affectedRange,
      newBoundaryIndex
    };
  }

  private calculateAffectedRange(changes: CodeMirrorChangeDesc): { start: number; end: number } {
    let minStart = Infinity;
    let maxEnd = 0;

    changes.iterChanges((fromA: number, toA: number, fromB: number, toB: number) => {
      minStart = Math.min(minStart, fromA);
      maxEnd = Math.max(maxEnd, toB);
    });

    return {
      start: minStart === Infinity ? 0 : minStart,
      end: maxEnd
    };
  }

  /**
   * 获取当前段落映射
   */
  getCurrentSegments(): Map<string, { content: string; startOffset: number; endOffset: number }> {
    return new Map(this.lastSegments);
  }

  /**
   * 强制完全重新解析（回退方案）
   */
  forceReparse(content: string): IncrementalParseResult {
    console.warn('Falling back to full reparse');
    
    const oldSegments = new Map(this.lastSegments);
    this.initializeSegments(content);
    
    const changes: SegmentChange[] = [];
    
    // 标记所有旧段落为删除
    for (const [oldId, oldSegment] of Array.from(oldSegments)) {
      changes.push({
        type: 'remove',
        segmentId: oldId,
        content: oldSegment.content,
        startOffset: oldSegment.startOffset,
        endOffset: oldSegment.endOffset
      });
    }
    
    // 标记所有新段落为添加
    for (const [newId, newSegment] of Array.from(this.lastSegments)) {
      changes.push({
        type: 'add',
        segmentId: newId,
        content: newSegment.content,
        startOffset: newSegment.startOffset,
        endOffset: newSegment.endOffset
      });
    }

    this.lastContent = content;
    this.boundaryIndex = new BoundaryIndex(content);

    return {
      changes,
      affectedRange: { start: 0, end: content.length },
      newBoundaryIndex: this.boundaryIndex
    };
  }

  /**
   * 调试信息
   */
  debug(): void {
    console.log('IncrementalParser Debug Info:');
    console.log(`Segments count: ${this.lastSegments.size}`);
    console.log(`Content length: ${this.lastContent.length}`);
    console.log('Segments:', Array.from(this.lastSegments.keys()));
  }
} 