'use client';

import React, { useEffect, useRef, useState, useMemo } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';

interface M2RendererProps {
  content: string;
  onChange?: (content: string) => void;
  onTOCGenerated?: (toc: any) => void;
  onScrollPositionChanged?: (position: any) => void;
}

export function NativeDOMRendererM2Fallback({ 
  content, 
  onChange, 
  onTOCGenerated, 
  onScrollPositionChanged 
}: M2RendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [renderedHTML, setRenderedHTML] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [m2Status, setM2Status] = useState<string>('🔄 简化模式初始化...');

  // Markdown处理器
  const processor = useMemo(
    () =>
      unified()
        .use(remarkParse)
        .use(remarkGfm)
        .use(remarkRehype, { allowDangerousHtml: true })
        .use(rehypeHighlight)
        .use(rehypeRaw)
        .use(rehypeStringify),
    []
  );

  // 简化的防抖处理
  const debounceTimer = useRef<NodeJS.Timeout>();

  // 渲染内容
  const renderContent = async (newContent: string) => {
    if (isProcessing) return;
    
    setIsProcessing(true);
    setM2Status('🔄 渲染中...');

    try {
      console.log('🎨 简化渲染开始:', newContent.length, '字符');
      
      // 处理Markdown
      const result = await processor.process(newContent);
      const html = String(result);
      
      // 基础安全处理
      const secureHTML = html
        .replace(/(<a[^>]*?)>/gi, '$1 rel="noopener noreferrer" target="_blank">')
        .replace(/(<img[^>]*?)>/gi, '$1 loading="lazy">');
      
      setRenderedHTML(secureHTML);
      setM2Status('✅ 渲染完成');
      
      console.log('✅ 简化渲染完成');
      
    } catch (error) {
      console.error('渲染失败:', error);
      setRenderedHTML(`<p style="color: red;">渲染错误: ${error}</p>`);
      setM2Status('❌ 渲染失败');
    } finally {
      setIsProcessing(false);
    }
  };

  // 防抖处理内容变化
  useEffect(() => {
    if (!content) {
      setRenderedHTML('<p>没有内容</p>');
      return;
    }

    // 清除之前的定时器
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    // 设置新的防抖
    debounceTimer.current = setTimeout(() => {
      renderContent(content);
    }, 100);

    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, [content]);

  // 更新DOM
  useEffect(() => {
    if (!containerRef.current || !renderedHTML) return;

    try {
      containerRef.current.innerHTML = renderedHTML;
      console.log('📄 DOM更新完成');
    } catch (error) {
      console.error('DOM更新失败:', error);
      if (containerRef.current) {
        containerRef.current.innerHTML = '<p>DOM更新失败</p>';
      }
    }
  }, [renderedHTML]);

  // 调试接口
  useEffect(() => {
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      (window as any).m2FallbackDebug = {
        getStatus: () => m2Status,
        getRenderedHTML: () => renderedHTML,
        getContentLength: () => content?.length || 0,
        forceRender: () => renderContent(content),
        debugInfo: () => {
          console.log('=== M2简化模式调试信息 ===');
          console.log('状态:', m2Status);
          console.log('内容长度:', content?.length || 0);
          console.log('HTML长度:', renderedHTML?.length || 0);
          console.log('是否处理中:', isProcessing);
        }
      };
      
      console.log('🔧 M2简化模式调试接口已注册');
    }
  }, [m2Status, renderedHTML, content, isProcessing]);

  return (
    <div className="relative">
      {/* 简化模式状态指示器 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-20 right-16 bg-orange-600 text-white px-2 py-1 rounded text-xs z-50">
          简化模式: {m2Status}
        </div>
      )}
      
      <div 
        ref={containerRef}
        className="prose prose-sm sm:prose-base lg:prose-lg xl:prose-xl 2xl:prose-2xl mx-auto focus:outline-none"
        style={{
          maxWidth: 'none',
          lineHeight: '1.7',
          fontSize: '14px',
          padding: '16px',
          minHeight: '200px'
        }}
      >
        {!renderedHTML && (
          <div className="text-gray-500 text-center py-8">
            {isProcessing ? '正在渲染...' : '准备渲染内容...'}
          </div>
        )}
      </div>
    </div>
  );
}

export default NativeDOMRendererM2Fallback; 