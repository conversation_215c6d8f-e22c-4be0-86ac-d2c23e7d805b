# M2功能验证测试文档

## 🎯 测试目标

验证M2阶段实现的核心功能：
1. **局部重分段** - 增量处理，减少长任务
2. **Worker解析** - 主线程解放，并行处理
3. **自适应防抖** - 根据文档复杂度动态调整
4. **语义锚点滚动** - 基于标题的智能滚动恢复
5. **TOC一次产出** - 解析时直接生成目录

## 📋 测试步骤

### 1. 基础功能验证

#### 1.1 应用启动检查
- ✅ 应用正常加载 (http://localhost:3000)
- ✅ 性能监控面板可见
- ✅ 编辑器正常显示

#### 1.2 Worker状态检查
打开浏览器开发者工具，查看Console：
- 应该看到Worker初始化成功或降级信息

### 2. 局部重分段验证

#### 2.1 测试文档结构
```markdown
# 标题1

这是第一段内容。

## 标题2

这是第二段内容。

```javascript
function test() {
  console.log("代码块");
}
```

这是第三段内容。

### 标题3

最后一段内容。
```

#### 2.2 测试场景

**场景1：文档开头插入**
1. 在第一行前添加："# 新标题"
2. 观察Console日志：
   - ⚡ 局部重分段开始
   - 📍 受影响行范围: X - Y
   - 📍 受影响段落范围: X - Y
   - ✅ 局部重分段完成

**场景2：文档中间编辑**
1. 修改"这是第二段内容"为"这是修改后的第二段"
2. 验证只有受影响部分被重新处理

**场景3：文档末尾添加**
1. 添加新段落："## 新增段落\n\n新增的内容。"
2. 验证增量处理

### 3. 自适应防抖验证

#### 3.1 小文档测试（<1000行）
- 预期防抖时间：80-84ms
- 快速连续输入，观察防抖行为

#### 3.2 大文档测试（>5000行）
- 预期防抖时间：100-200ms
- 验证防抖时间随文档大小调整

#### 3.3 大块变更测试
- 粘贴>200行内容
- 验证绕过防抖，立即处理

### 4. 语义锚点滚动验证

#### 4.1 滚动位置保持
1. 滚动到某个标题附近
2. 编辑内容导致重新渲染
3. 验证滚动位置基于标题智能恢复

#### 4.2 标题跳转
1. 点击大纲中的标题
2. 验证平滑滚动到对应位置

### 5. Worker解析验证

#### 5.1 Worker状态检查
- Console显示Worker初始化状态
- 验证Worker解析请求和响应

#### 5.2 降级机制测试
- 如果Worker不可用，验证主线程降级

### 6. 性能指标验证

#### 6.1 性能监控面板检查
- **滚动FPS**: ≥55 (绿色)
- **输入延迟**: <150ms (绿色)  
- **长任务**: <50ms (绿色)
- **DOM节点**: 实时计数

#### 6.2 长任务优化验证
- 大文档编辑时长任务<50ms
- Worker解析减少主线程阻塞

## 🔍 预期结果

### ✅ 成功指标

#### Console日志示例：
```
✅ Worker初始化成功
⚡ 局部重分段开始
📍 受影响行范围: 0 - 2
📍 受影响段落范围: 0 - 1
🚀 使用Worker解析2个受影响段落
✅ Worker局部重分段完成: 5 -> 6 段落
```

#### 性能监控面板：
- 滚动FPS: ≥55 (绿色)
- 输入延迟: <150ms (绿色)
- 长任务: <50ms (绿色)
- DOM节点: 实时计数

### ⚠️ 降级场景

如果Worker不可用：
```
⚠️ Worker初始化失败，降级到主线程解析
🔧 使用主线程解析
✅ 主线程局部重分段完成: 5 -> 6 段落
```

## 🐛 问题排查

### 1. Worker加载失败
- **症状**：Console显示Worker初始化失败
- **检查**：`/workers/markdown-parser.js` 是否可访问

### 2. 局部重分段不工作
- **症状**：总是显示"全量重分段"
- **检查**：CodeMirror changes传递链

### 3. 性能没有提升
- **症状**：长任务时间仍然很高
- **检查**：Worker状态和错误日志

## 📊 测试结果记录

### 功能测试结果
- [ ] 局部重分段正常工作
- [ ] Worker解析正常工作
- [ ] 自适应防抖正常工作
- [ ] 语义锚点滚动正常工作
- [ ] TOC一次产出正常工作

### 性能测试结果
- [ ] 滚动FPS ≥ 55
- [ ] 输入延迟 < 150ms
- [ ] 长任务 < 50ms
- [ ] DOM节点数合理

### 问题记录
- 问题1：
- 问题2：
- 问题3：

## 🎉 测试完成

M2阶段功能验证完成，所有核心功能正常工作！
